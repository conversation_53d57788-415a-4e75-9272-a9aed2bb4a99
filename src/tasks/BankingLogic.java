package tasks;

import Main.Main;
import Utils.KazeUtils;
import org.dreambot.api.methods.container.impl.Inventory;
import org.dreambot.api.methods.container.impl.bank.Bank;
import org.dreambot.api.methods.container.impl.bank.BankLocation;
import org.dreambot.api.methods.container.impl.equipment.Equipment;
import org.dreambot.api.methods.grandexchange.GrandExchange;
import org.dreambot.api.methods.interactive.Players;
import org.dreambot.api.methods.map.Tile;
import org.dreambot.api.methods.walking.impl.Walking;
import org.dreambot.api.utilities.Logger;
import org.dreambot.api.utilities.Sleep;
import org.dreambot.api.wrappers.items.Item;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Centralized banking logic for tasks
 * Handles withdrawing required items for tasks
 */
public class BankingLogic {

    // Map to track tasks that have already been handled
    private static final Map<String, Long> handledTasks = new HashMap<>();

    // Time in milliseconds after which a task should be handled again
    private static final long TASK_HANDLING_TIMEOUT = 60000; // 1 minute

    /**
     * Checks if all required items are available in bank and inventory.
     * If not, goes to Grand Exchange to purchase missing items.
     *
     * @param task The task to check requirements for
     * @return true if all items are available or successfully purchased, false if purchase failed
     */
    public static boolean checkAndPurchaseRequiredItems(AbstractTask task) {
        // If task has no requirements, return true
        if (!task.hasRequiredItems()) {
            return true;
        }

        Logger.log("Checking required items for task: " + task.getName());
        Main.action = "Checking required items for " + task.getName();

        // First, walk to the bank to check what we have - loop until successful
        Logger.log("Walking to bank to check required items");
        Main.action = "Walking to bank to check required items";

        // Cache bank location to avoid repeated API calls
        BankLocation nearest = BankLocation.getNearest();
        if (nearest == null) {
            Logger.log("No nearest bank found");
            return false;
        }

        // Loop infinitely until bank is opened
        boolean bankingAttempted = false;
        while (!Bank.isOpen()) {
            // Cache player position to avoid repeated API calls
            Tile playerTile = Players.getLocal().getTile();
            boolean atBank = nearest.getArea(5).contains(playerTile);
            boolean isMoving = Players.getLocal().isMoving();

            if (atBank) {
                // We're at the bank, try to open it
                if (!bankingAttempted) {
                    Logger.log("At bank, attempting to open...");
                    Main.action = "Opening bank to check required items";
                    bankingAttempted = true;
                }

                KazeUtils.openNearestBank();

                // Wait for bank to open with shorter timeout for responsiveness
                if (Sleep.sleepUntil(Bank::isOpen, 3000)) {
                    break; // Bank opened successfully
                }

                // Brief pause before retrying
                Sleep.sleep(500, 1000);
            } else {
                // We're not at the bank, walk there
                if (!isMoving) {
                    if (!bankingAttempted) {
                        Logger.log("Walking to nearest bank...");
                        bankingAttempted = true;
                    }
                    Walking.walk(nearest.getArea(1).getTile());
                }

                // Wait for movement or arrival with optimized timeout
                Sleep.sleepUntil(() -> {
                    Tile currentTile = Players.getLocal().getTile();
                    return nearest.getArea(5).contains(currentTile) ||
                           !Players.getLocal().isMoving();
                }, 8000);

                // Reset attempt flag when we start moving
                if (isMoving) {
                    bankingAttempted = false;
                }
            }
        }

        Logger.log("Successfully opened bank to check required items");

        // Check what items we're missing
        List<String> missingItems = new ArrayList<>();
        Map<String, Integer> missingQuantities = new HashMap<>();

        // Cache equipment items to avoid repeated API calls
        Map<String, Boolean> equippedItems = new HashMap<>();

        // Check inventory requirements
        Map<String, Integer> inventoryRequirements = task.getRequiredInventoryItems();
        for (Map.Entry<String, Integer> entry : inventoryRequirements.entrySet()) {
            String itemName = entry.getKey();
            int requiredQuantity = entry.getValue();

            // Skip optional items (quantity 0)
            if (requiredQuantity == 0) {
                continue;
            }

            // Cache equipment check to avoid repeated API calls
            if (!equippedItems.containsKey(itemName)) {
                equippedItems.put(itemName, Equipment.contains(itemName));
            }

            // Check total available (inventory + equipment + bank)
            int totalAvailable = Inventory.count(itemName) + Bank.count(itemName);
            if (equippedItems.get(itemName)) {
                totalAvailable++;
            }

            if (totalAvailable < requiredQuantity) {
                int missingAmount = requiredQuantity - totalAvailable;
                missingItems.add(itemName);
                missingQuantities.put(itemName, missingAmount);
                if (Main.DEBUG) {
                    Logger.log("Missing " + missingAmount + "x " + itemName + " (have " + totalAvailable + ", need " + requiredQuantity + ")");
                }
            }
        }

        // Check equipment requirements
        List<String> equipmentRequirements = task.getRequiredEquipmentItems();
        for (String itemName : equipmentRequirements) {
            // Cache equipment check if not already done
            if (!equippedItems.containsKey(itemName)) {
                equippedItems.put(itemName, Equipment.contains(itemName));
            }

            // Check if we have it equipped, in inventory, or in bank
            if (!equippedItems.get(itemName) && !Inventory.contains(itemName) && !Bank.contains(itemName)) {
                missingItems.add(itemName);
                missingQuantities.put(itemName, 1);
                if (Main.DEBUG) {
                    Logger.log("Missing equipment: " + itemName);
                }
            }
        }

        // If we have all items, return true
        if (missingItems.isEmpty()) {
            Logger.log("All required items are available");
            return true;
        }

        // Close bank before going to GE
        if (Bank.isOpen()) {
            Bank.close();
            Sleep.sleepUntil(() -> !Bank.isOpen(), 3000);
        }

        // Go to Grand Exchange to purchase missing items
        Logger.log("Going to Grand Exchange to purchase missing items: " + missingItems);
        Main.action = "Going to GE to buy missing items";

        // Loop infinitely until we successfully reach and open the Grand Exchange
        Logger.log("Walking to Grand Exchange to purchase items...");
        Main.action = "Walking to Grand Exchange";

        // Loop infinitely until GE is opened with reduced logging
        boolean geAttempted = false;
        while (!GrandExchange.isOpen()) {
            if (!geAttempted) {
                Logger.log("Attempting to reach and open Grand Exchange...");
                Main.action = "Walking to GE to buy items";
                geAttempted = true;
            }

            if (KazeUtils.openGrandExchange()) {
                Logger.log("Successfully reached and opened Grand Exchange");
                break; // Exit the loop when successful
            } else {
                // Reduce sleep time for better responsiveness
                Sleep.sleep(1000, 1500);
            }
        }

        // Purchase each missing item
        for (String itemName : missingItems) {
            int quantity = missingQuantities.get(itemName);
            if (!purchaseItem(itemName, quantity)) {
                Logger.log("Failed to purchase " + itemName + ", stopping script");
                return false;
            }
        }

        // Wait for all purchases to complete and collect items
        if (!waitForPurchasesAndCollect()) {
            Logger.log("Failed to collect purchased items");
            return false;
        }

        // Close GE
        if (GrandExchange.isOpen()) {
            GrandExchange.close();
            Sleep.sleepUntil(() -> !GrandExchange.isOpen(), 3000);
        }

        Logger.log("Successfully purchased all missing items");
        return true;
    }

    /**
     * Purchases an item from the Grand Exchange
     *
     * @param itemName The name of the item to purchase
     * @param quantity The quantity to purchase
     * @return true if purchase was successful, false otherwise
     */
    private static boolean purchaseItem(String itemName, int quantity) {
        Logger.log("Attempting to purchase " + quantity + "x " + itemName);
        Main.action = "Purchasing " + quantity + "x " + itemName;

        // Find an open slot
        int openSlot = GrandExchange.getFirstOpenSlot();
        if (openSlot == -1) {
            Logger.log("No open GE slots available");
            return false;
        }

        // Open buy screen
        if (!GrandExchange.openBuyScreen(openSlot)) {
            Logger.log("Failed to open buy screen for slot " + openSlot);
            return false;
        }

        Sleep.sleepUntil(GrandExchange::isBuyOpen, 3000);
        if (!GrandExchange.isBuyOpen()) {
            Logger.log("Buy screen did not open");
            return false;
        }

        // Get market price and set a competitive price (110% of market price)
        int marketPrice = KazeUtils.getItemPrice(itemName);
        int buyPrice;
        if (marketPrice > 0) {
            buyPrice = (int) (marketPrice * 1.1); // 110% of market price for faster purchase
        } else {
            // Fallback price if we can't get market price
            buyPrice = 1000; // Default reasonable price
            Logger.log("Could not get market price for " + itemName + ", using default price: " + buyPrice);
        }

        // Purchase the item
        boolean success = GrandExchange.buyItem(itemName, quantity, buyPrice);
        if (success) {
            Logger.log("Successfully placed buy order for " + quantity + "x " + itemName + " at " + buyPrice + " gp each");
        } else {
            Logger.log("Failed to place buy order for " + itemName);
        }

        return success;
    }

    /**
     * Waits for all purchases to complete and collects the items
     *
     * @return true if all items were collected successfully, false otherwise
     */
    private static boolean waitForPurchasesAndCollect() {
        Logger.log("Waiting for purchases to complete...");
        Main.action = "Waiting for GE purchases";

        // Wait up to 2 minutes for purchases to complete
        long startTime = System.currentTimeMillis();
        long timeout = 120000; // 2 minutes

        boolean collectingLogged = false;
        long lastCollectCheck = 0;

        while (System.currentTimeMillis() - startTime < timeout) {
            // Reduce API calls by checking every 3 seconds
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastCollectCheck > 3000) {
                // Check if any items are ready to collect
                if (GrandExchange.isReadyToCollect()) {
                    if (!collectingLogged) {
                        Logger.log("Items ready to collect");
                        Main.action = "Collecting purchased items";
                        collectingLogged = true;
                    }

                    // Collect to inventory
                    if (GrandExchange.collect()) {
                        Logger.log("Successfully collected items from GE");
                        Sleep.sleep(500, 1000); // Reduced wait time

                        // Check if there are more items to collect
                        if (!GrandExchange.isReadyToCollect()) {
                            return true; // All items collected
                        }
                    }
                }
                lastCollectCheck = currentTime;
            }

            // Shorter sleep for better responsiveness
            Sleep.sleep(1000, 1500);
        }

        Logger.log("Timeout waiting for GE purchases to complete");
        return false;
    }

    /**
     * Handles banking for a task
     * Goes to the bank, withdraws required items, and returns to the task location
     *
     * @param task The task to handle banking for
     * @return true if banking was successful, false otherwise
     */
    public static boolean handleBanking(AbstractTask task) {
        // If task has no requirements, return true
        if (!task.hasRequiredItems()) {
            return true;
        }

        // Check if the current active task has changed (task was skipped)
        if (Main.currentActiveTask != task) {
            Logger.log("Task changed during banking, aborting banking for: " + task.getName());
            return false;
        }

        // Check if we've already handled this task recently
        String taskKey = task.getName() + "_" + task.getSkill().name();
        long currentTime = System.currentTimeMillis();
        if (handledTasks.containsKey(taskKey)) {
            long lastHandled = handledTasks.get(taskKey);
            if (currentTime - lastHandled < TASK_HANDLING_TIMEOUT) {
                // We've handled this task recently, skip banking
                if (Main.DEBUG) {
                    Logger.log("Already handled banking for task recently: " + task.getName());
                }
                return true;
            }
        }

        // First check if we already have all required items (without opening bank)
        if (hasRequiredItems(task)) {
            return true;
        }

        // Check and purchase required items if missing (walks to bank first, then GE if needed)
        if (!checkAndPurchaseRequiredItems(task)) {
            Logger.log("Failed to obtain required items for task: " + task.getName());
            return false;
        }

        // Log that we're going to the bank
        Logger.log("Going to bank to get required items for task: " + task.getName());
        Main.action = "Banking for " + task.getName();

        // Open the nearest bank
        if (!Bank.isOpen()) {
            // Check if task has been skipped
            if (Main.currentActiveTask != task) {
                Logger.log("Task changed during banking, aborting banking for: " + task.getName());
                return false;
            }

            // Try to open the nearest bank
            KazeUtils.openNearestBank();

            // If still not open, try walking to the nearest bank
            if (!Bank.isOpen()) {
                // Check again if task has been skipped
                if (Main.currentActiveTask != task) {
                    Logger.log("Task changed during banking, aborting banking for: " + task.getName());
                    return false;
                }

                BankLocation nearest = BankLocation.getNearest();
                if (nearest != null) {
                    if (nearest.getArea(1).contains(Players.getLocal().getTile())) {
                        Main.action = "Walking to bank to get required items";
                        KazeUtils.openNearestBank();
                    } else {
                        if (!Players.getLocal().isMoving()
                            && !nearest.getArea(1).contains(Players.getLocal().getTile())) {
                            Walking.walk(nearest.getArea(1).getTile());
                        }
                    }
                }
            }

            // If still not open, return false
            if (!Bank.isOpen()) {
                Logger.log("Failed to open bank to get required items");
                return false;
            }
        }

        // Deposit all items first to make space (if task supports banking)
        if (!Inventory.isEmpty()) {
            // Check if task has been skipped
            if (Main.currentActiveTask != task) {
                Logger.log("Task changed during banking, aborting banking for: " + task.getName());
                return false;
            }

            Main.action = "Depositing items to make space";
            Bank.depositAllItems();
            Sleep.sleepUntil(Inventory::isEmpty, 5000);
            Bank.depositAllEquipment();

            if (!Inventory.isEmpty()) {
                Logger.log("Failed to deposit items");
                return false;
            }
        }

        // STEP 1: Withdraw ALL required items (both inventory and equipment) first

        // Check if task has been skipped
        if (Main.currentActiveTask != task) {
            Logger.log("Task changed during banking, aborting banking for: " + task.getName());
            return false;
        }

        // Withdraw required inventory items
        Map<String, Integer> inventoryRequirements = task.getRequiredInventoryItems();
        for (Map.Entry<String, Integer> entry : inventoryRequirements.entrySet()) {
            String itemName = entry.getKey();
            int requiredQuantity = entry.getValue();

            // Skip optional items (quantity 0)
            if (requiredQuantity == 0) {
                continue;
            }

            // Check if we already have enough of this item
            int currentCount = Inventory.count(itemName);
            if (Equipment.contains(itemName)) {
                currentCount++; // Count equipped item
            }

            if (currentCount < requiredQuantity) {
                // We need to withdraw this item
                int amountToWithdraw = requiredQuantity - currentCount;

                if (!Bank.contains(itemName)) {
                    Logger.log("Bank does not contain required item: " + itemName);
                    return false;
                }

                // Store the current count before withdrawal
                int beforeCount = Inventory.count(itemName);

                Main.action = "Withdrawing " + itemName + " (" + amountToWithdraw + ")";
                Bank.withdraw(itemName, amountToWithdraw);
                Sleep.sleepUntil(() -> Inventory.count(itemName) > beforeCount, 3000);

                if (Inventory.count(itemName) < beforeCount + amountToWithdraw) {
                    Logger.log("Failed to withdraw required quantity of " + itemName);
                    return false;
                }
            }
        }

        // Withdraw required equipment items (if not already in inventory)
        List<String> equipmentRequirements = task.getRequiredEquipmentItems();
        for (String itemName : equipmentRequirements) {
            // Skip if already equipped or in inventory
            if (Equipment.contains(itemName) || Inventory.contains(itemName)) {
                continue;
            }

            // Not in inventory, try to withdraw from bank
            if (!Bank.contains(itemName)) {
                Logger.log("Bank does not contain required equipment: " + itemName);
                return false;
            }

            Main.action = "Withdrawing " + itemName + " to equip";
            Bank.withdraw(itemName, 1);
            Sleep.sleepUntil(() -> Inventory.contains(itemName), 3000);

            if (!Inventory.contains(itemName)) {
                Logger.log("Failed to withdraw equipment: " + itemName);
                return false;
            }
        }
        // STEP 2: Now equip all equipment items from inventory
        for (String itemName : equipmentRequirements) {
            // Skip if already equipped
            if (Equipment.contains(itemName)) {
                continue;
            }

            // Check if in inventory
            Item item = Inventory.get(itemName);
            if (item != null) {
                Main.action = "Equipping " + itemName;
                if (item.hasAction("Wear")) {
                    item.interact("Wear");
                } else if (item.hasAction("Wield")) {
                    item.interact("Wield");
                } else if (item.hasAction("Equip")) {
                    item.interact("Equip");
                }
                Sleep.sleepUntil(() -> Equipment.contains(itemName), 3000);

                if (!Equipment.contains(itemName)) {
                    Logger.log("Failed to equip: " + itemName);
                    return false;
                }
            } else {
                Logger.log("Required equipment not in inventory after withdrawal: " + itemName);
                return false;
            }
        }

        Logger.log("Successfully obtained all required items for task: " + task.getName());

        // Return to task location if specified
        if (task.getTaskLocation() != null || task.getTaskArea() != null) {
            Main.action = "Returning to task location";

            // Check if we're already at the task location
            if (!task.isAtTaskLocation(2)) {
                // Try to get a random tile within the task area, or use the exact tile
                Tile targetTile = task.getRandomTaskTile();
                if (targetTile != null) {
                    Walking.walk(targetTile);
                }
            }
        }

        handledTasks.put(taskKey, System.currentTimeMillis());

        return true;
    }

    /**
     * Handles banking for a task when inventory is full
     * Goes to the bank, deposits all items except those needed for the task
     *
     * @param task The task to handle banking for
     * @return true if banking was successful, false otherwise
     */

    /**
     * Check if all required items for a task are available (either in inventory or equipped)
     *
     * @param task The task to check requirements for
     * @return true if all requirements are met, false otherwise
     */
    public static boolean hasRequiredItems(AbstractTask task) {
        // If task has no requirements, return true
        if (!task.hasRequiredItems()) {
            return true;
        }

        // Check inventory requirements
        Map<String, Integer> inventoryRequirements = task.getRequiredInventoryItems();
        for (Map.Entry<String, Integer> entry : inventoryRequirements.entrySet()) {
            String itemName = entry.getKey();
            int requiredQuantity = entry.getValue();

            // Skip optional items (quantity 0)
            if (requiredQuantity == 0) {
                continue;
            }

            // Check if we have enough of this item
            int currentQuantity = Inventory.count(itemName);
            if (Equipment.contains(itemName)) {
                currentQuantity++; // Count equipped item
            }

            if (currentQuantity < requiredQuantity) {
                if (Main.DEBUG) {
                    Logger.log("Missing inventory item: " + itemName + " (have " + currentQuantity +
                              ", need " + requiredQuantity + ")");
                }
                return false;
            }
        }

        // Check equipment requirements
        List<String> equipmentRequirements = task.getRequiredEquipmentItems();
        for (String itemName : equipmentRequirements) {
            if (!Equipment.contains(itemName)) {
                if (Main.DEBUG) {
                    Logger.log("Missing equipment item: " + itemName);
                }
                return false;
            }
        }

        // All requirements met
        return true;
    }
}
