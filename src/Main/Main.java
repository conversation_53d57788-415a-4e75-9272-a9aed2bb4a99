package Main;

import static org.dreambot.api.data.ClientLayout.RESIZABLE_CLASSIC;
import static org.dreambot.api.utilities.Images.loadImage;

import Utils.DrawMouseUtil;
import Utils.KazeUtils;
import Utils.ScriptTimer;
import Utils.SkillTracker;
import gui.GUI;
import org.dreambot.api.ClientSettings;
import org.dreambot.api.methods.interactive.Players;
import org.dreambot.api.methods.map.Tile;
import org.dreambot.api.methods.skills.Skills;
import org.dreambot.api.utilities.Sleep;
import org.dreambot.api.wrappers.widgets.message.Message;
import tasks.BankingLogic;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.image.BufferedImage;
import java.awt.AlphaComposite;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.List;
import javax.imageio.ImageIO;
import javax.swing.*;
import javax.swing.SwingUtilities;
import org.dreambot.api.Client;
import org.dreambot.api.data.GameState;
import org.dreambot.api.input.Mouse;
import org.dreambot.api.methods.skills.Skill;
import org.dreambot.api.methods.widget.Widget;
import org.dreambot.api.methods.widget.Widgets;
import org.dreambot.api.script.Category;
import org.dreambot.api.script.ScriptManifest;
import org.dreambot.api.script.impl.TaskScript;
import org.dreambot.api.script.listener.ChatListener;
import org.dreambot.api.utilities.Logger;
import org.dreambot.api.wrappers.widgets.WidgetChild;
import tasks.AbstractTask;
import tasks.TaskManager;

@ScriptManifest(
    name = "Kazeh's F2P AIO BETA",
    description = "1-Click Account Builder",
    author = "Kazeh",
    version = 0.0,
    category = Category.MISC,
    image = "https://i.ibb.co/7tTgKXh1/Screenshot-2025-04-22-130631.png")
public class Main extends TaskScript implements ChatListener, MouseListener {
  public static String text = "Kazeh's F2P AIO BETA";
  public static boolean DEBUG = false;
  public static boolean shuffletasks;
  public static String action = "Waiting..";
  public static String AntiBanAction = "";
  public static String currentTask = "Initalizing..";
  public static String taskTimeRemaining = "";
  public static boolean antibanEnabled;
  public static int botState;
  public static String afkTimer;
  public static boolean afkEnabled = false;
  public static Map<Skill, Integer> targetSkills = new HashMap<>();
  public static ArrayList<Object> selectedTasks;
  public static boolean useTaskOptimization;
  public static boolean breaksEnabled;
  public static int breakFrequency = 60;
  public static int breakDuration = 5;
  public static boolean logoutAfterTime;
  public static int logoutTime = 120;
  public static boolean lootOnlyOurKills = true;

  // Anti-ban settings
  public static Map<String, Object> antiBanSettings = new HashMap<>();

  // Banking settings - stores task names that should use banking
  public static Set<String> tasksThatBank = new HashSet<>();

  // Current task being executed
  public static AbstractTask currentActiveTask = null;

  // Flag to indicate if the bot is running
  public static boolean running = false;
  public static Map<Skill, BufferedImage> skillIcons;
  // Item buying system
  public static boolean shouldBuyItems = true;
  public static boolean needToBuyItems = false;
  public static List<String> itemsToBuy = new ArrayList<>();
  private final DrawMouseUtil drawMouseUtil = new DrawMouseUtil();
  private final long startTime = System.currentTimeMillis();
  Color fontcolor = Color.WHITE;

  // Double buffering for paint to reduce flickering
  private BufferedImage paintBuffer;
  private long lastPaintTime = 0;
  private final int PAINT_UPDATE_RATE = 250; // Update paint every 250ms (4 FPS) to reduce CPU usage

  // Store the total level icon separately
  private BufferedImage totalLevelIcon;

  // Cache frequently used values to reduce calculations
  private int cachedTotalLevel = 0;
  private int cachedGainedTotalLevels = 0;
  private long lastStatsUpdateTime = 0;
  private final int STATS_UPDATE_RATE = 1000; // Update stats every second

  // Skip task button
  private Rectangle skipTaskButton = new Rectangle();
  private long lastSkipTaskTime = 0;
  private final long SKIP_TASK_COOLDOWN = 1000; // 1 second cooldown between skips
  private boolean forceTaskSelection = false; // Flag to force immediate task selection

  // Task list caching to improve performance
  private List<AbstractTask> cachedTaskList = null;
  private long lastTaskListUpdateTime = 0;

  // Skill icons
  private BufferedImage attackIcon,
      strengthIcon,
      defenceIcon,
      rangedIcon,
      prayerIcon,
      magicIcon,
      hitpointsIcon,
      miningIcon,
      fishingIcon,
      woodcuttingIcon,
      farmingIcon,
      hunterIcon,
      smithingIcon,
      craftingIcon,
      fletchingIcon,
      cookingIcon,
      firemakingIcon,
      herbloreIcon,
      agilityIcon,
      thievingIcon,
      slayerIcon,
      runecraftIcon,
      constructionIcon;
  private Graphics graphics;

  // Add these fields to track task time more precisely
  private static long taskEndTime = 0;
  private static AbstractTask lastActiveTask = null;

  @Override
  public void onStart() {

    // Initialize the task system first
    Logger.log("Main: Initializing TaskManager");
    TaskManager.initializeTasks();

    // Initialize skill tracker
    Logger.log("Main: Initializing SkillTracker");
    SkillTracker.initialize();

    // Initialize script name first
    text = getManifest().name();
    Logger.log("Script name set to: " + text);

    // Load skill icons
    loadSkillIcons();

    // Initialize variables
    DEBUG = true;
    Main.AntiBanAction = "";
    Main.antibanEnabled = false;
    Main.afkEnabled = false;
    Main.afkTimer = KazeUtils.formatTime(0);
    Main.currentTask = "Initializing...";
    Main.running = false; // Make sure the bot is not running until Start is clicked

    // Initialize collections
    targetSkills = new HashMap<>();
    selectedTasks = new ArrayList<>();

    // Start timer
    ScriptTimer.startTimer();
    Mouse.setMouseAlgorithm(new Utils.KazeMouse());

    // Register mouse listener for button clicks
    Client.getCanvas().addMouseListener(this);

    // Create and show GUI - ONLY ONCE (after all initializations)
    try {
      Logger.log("Main: Creating GUI");
      SwingUtilities.invokeAndWait(
          () -> {
            try {
              GUI gui = new GUI(this);
              // Note: setVisible(true) is called in the GUI constructor
            } catch (Exception e) {
              Logger.log("Error creating GUI: " + e.getMessage());
              e.printStackTrace();
            }
          });
    } catch (Exception e) {
      Logger.log("Error in GUI thread: " + e.getMessage());
      e.printStackTrace();
    }

    Logger.log("Script started successfully");
  }

  private void loadSkillIcons() {
    // Create images directory in the script's config directory
    String configDir = System.getProperty("scripts.path") + File.separator + Main.text;
    File imagesDir = new File(configDir, "images");
    File skillIconsDir = new File(imagesDir, "icons");

    // Create directories if they don't exist
    if (!skillIconsDir.exists()) {
      boolean created = skillIconsDir.mkdirs();
      Logger.log("Created icons directory: " + created + " at " + skillIconsDir.getAbsolutePath());
    }

    // Clear existing icons map or create new one
    skillIcons = new HashMap<>();

    // Map of skill names to their wiki image URLs
    Map<Skill, String> skillImageUrls = new HashMap<>();
    skillImageUrls.put(Skill.ATTACK, "https://oldschool.runescape.wiki/images/Attack_icon.png");
    skillImageUrls.put(Skill.STRENGTH, "https://oldschool.runescape.wiki/images/Strength_icon.png");
    skillImageUrls.put(Skill.DEFENCE, "https://oldschool.runescape.wiki/images/Defence_icon.png");
    skillImageUrls.put(Skill.RANGED, "https://oldschool.runescape.wiki/images/Ranged_icon.png");
    skillImageUrls.put(Skill.PRAYER, "https://oldschool.runescape.wiki/images/Prayer_icon.png");
    skillImageUrls.put(Skill.MAGIC, "https://oldschool.runescape.wiki/images/Magic_icon.png");
    skillImageUrls.put(Skill.HITPOINTS, "https://oldschool.runescape.wiki/images/Hitpoints_icon.png");
    skillImageUrls.put(Skill.MINING, "https://oldschool.runescape.wiki/images/Mining_icon.png");
    skillImageUrls.put(Skill.FISHING, "https://oldschool.runescape.wiki/images/Fishing_icon.png");
    skillImageUrls.put(Skill.WOODCUTTING, "https://oldschool.runescape.wiki/images/Woodcutting_icon.png");
    skillImageUrls.put(Skill.COOKING, "https://oldschool.runescape.wiki/images/Cooking_icon.png");
    skillImageUrls.put(Skill.FIREMAKING, "https://oldschool.runescape.wiki/images/Firemaking_icon.png");
    skillImageUrls.put(Skill.SMITHING, "https://oldschool.runescape.wiki/images/Smithing_icon.png");
    skillImageUrls.put(Skill.CRAFTING, "https://oldschool.runescape.wiki/images/Crafting_icon.png");
    skillImageUrls.put(Skill.RUNECRAFTING, "https://oldschool.runescape.wiki/images/Runecraft_icon.png");

    // Initialize the skillIcons map
    skillIcons = new HashMap<>();

    Logger.log("Loading skill icons from local cache... Directory: " + skillIconsDir.getAbsolutePath());

    // First check if we have a serialized cache file for faster loading
    File cacheFile = new File(skillIconsDir, "icon_cache.dat");
    boolean loadedFromCache = false;

    if (cacheFile.exists()) {
      try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(cacheFile))) {
        // Read the serialized map of skill icons
        Map<String, SerializableBufferedImage> cachedIcons = (Map<String, SerializableBufferedImage>) ois.readObject();
        Logger.log("Found cached icons: " + cachedIcons.size());

        // Load the total level icon from cache
        SerializableBufferedImage ttlIcon = cachedIcons.get("total_level");
        if (ttlIcon != null) {
          totalLevelIcon = ttlIcon.getImage();
          Logger.log("Loaded total level icon from cache");
        }

        // Load skill icons from cache
        for (Skill skill : skillImageUrls.keySet()) {
          SerializableBufferedImage iconWrapper = cachedIcons.get(skill.name());
          if (iconWrapper != null) {
            BufferedImage icon = iconWrapper.getImage();
            if (icon != null) {
              skillIcons.put(skill, icon);
              Logger.log("Loaded " + skill.name() + " icon from cache");
            } else {
              Logger.log("Failed to load " + skill.name() + " icon from cache: null image");
            }
          } else {
            Logger.log("No cached icon found for " + skill.name());
          }
        }

        Logger.log("Successfully loaded " + skillIcons.size() + " icons from cache");
        loadedFromCache = skillIcons.size() > 0;
      } catch (Exception e) {
        Logger.log("Error loading from cache: " + e.getMessage() + ". Will load from files.");
        e.printStackTrace(); // Add stack trace for better debugging
      }
    }

    // If we couldn't load from cache, load from individual files
    if (!loadedFromCache) {
      // Load total level icon
      try {
        File ttlIconFile = new File(skillIconsDir, "total_level_icon.png");

        // If file doesn't exist, download it
        if (!ttlIconFile.exists()) {
          Logger.log("Downloading total level icon...");
          downloadImage("https://oldschool.runescape.wiki/images/Stats_icon.png", ttlIconFile);
        }

        // Try to load from file
        if (ttlIconFile.exists()) {
          try {
            totalLevelIcon = ImageIO.read(ttlIconFile);
            if (totalLevelIcon != null) {
              totalLevelIcon = resizeImage(totalLevelIcon);
              Logger.log("Loaded total level icon from file");
            }
          } catch (Exception e) {
            Logger.log("Error reading total level icon from file: " + e.getMessage());
          }
        }

        // If still null, create fallback
        if (totalLevelIcon == null) {
          Logger.log("Creating fallback total level icon");
          totalLevelIcon = createFallbackTotalLevelIcon();
        }
      } catch (Exception e) {
        Logger.log("Error with total level icon: " + e.getMessage());
        totalLevelIcon = createFallbackTotalLevelIcon();
      }

      // Load skill icons from files
      for (Map.Entry<Skill, String> entry : skillImageUrls.entrySet()) {
        Skill skill = entry.getKey();
        String url = entry.getValue();
        String fileName = skill.name().toLowerCase() + "_icon.png";
        File iconFile = new File(skillIconsDir, fileName);

        try {
          // Check if the file already exists
          if (iconFile.exists()) {
            Logger.log("Found icon file for " + skill.name() + ": " + iconFile.getAbsolutePath());
            // Load the image from file
            BufferedImage icon = ImageIO.read(iconFile);
            if (icon != null) {
              skillIcons.put(skill, resizeImage(icon));
              Logger.log("Successfully loaded icon for " + skill.name());
            } else {
              Logger.log("Failed to load icon for " + skill.name() + ": ImageIO.read returned null");
            }
          } else {
            Logger.log("Icon file not found for " + skill.name() + ": " + iconFile.getAbsolutePath());
          }
        } catch (IOException e) {
          Logger.log("Error loading " + skill.name() + " icon from file: " + e.getMessage());
        }
      }

      Logger.log("Loaded " + skillIcons.size() + " skill icons from files");

      // Download any missing icons
      downloadMissingIcons(skillImageUrls, skillIconsDir);

      // Save the loaded icons to cache for faster loading next time
      try {
        Map<String, SerializableBufferedImage> iconCache = new HashMap<>();

        // Add total level icon to cache
        if (totalLevelIcon != null) {
          iconCache.put("total_level", new SerializableBufferedImage(totalLevelIcon));
        }

        // Add skill icons to cache
        for (Map.Entry<Skill, BufferedImage> entry : skillIcons.entrySet()) {
          if (entry.getValue() != null) {
            iconCache.put(entry.getKey().name(), new SerializableBufferedImage(entry.getValue()));
          }
        }

        // Ensure parent directory exists
        if (!skillIconsDir.exists()) {
          skillIconsDir.mkdirs();
        }

        // Save to cache file
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(cacheFile))) {
          oos.writeObject(iconCache);
          Logger.log("Saved " + iconCache.size() + " icons to cache for faster loading");
        }
      } catch (Exception e) {
        Logger.log("Error saving icon cache: " + e.getMessage());
        e.printStackTrace(); // Add stack trace for better debugging
      }
    }

    // Log the status of all skill icons
    logSkillIconStatus();
  }

  /**
   * Log the status of all skill icons to help diagnose issues
   */
  private void logSkillIconStatus() {
    Logger.log("=== Skill Icon Status ====");
    Logger.log("Total icons loaded: " + skillIcons.size());

    for (Skill skill : Skill.values()) {
      BufferedImage icon = skillIcons.get(skill);
      if (icon != null) {
        Logger.log(skill.name() + ": LOADED (" + icon.getWidth() + "x" + icon.getHeight() + ")");
      } else {
        Logger.log(skill.name() + ": NOT LOADED");
      }
    }

    Logger.log("=========================");
  }

  /**
   * Downloads any missing skill icons in a background thread
   */
  private void downloadMissingIcons(Map<Skill, String> skillImageUrls, File skillIconsDir) {
    // Start a background thread to download any missing icons
    new Thread(() -> {
      try {
        Logger.log("Checking for missing skill icons...");
        int downloaded = 0;

        // Check for missing skill icons
        for (Map.Entry<Skill, String> entry : skillImageUrls.entrySet()) {
          Skill skill = entry.getKey();

          // If we don't have this icon in memory, try to download it
          if (!skillIcons.containsKey(skill)) {
            String url = entry.getValue();
            String fileName = skill.name().toLowerCase() + "_icon.png";
            File iconFile = new File(skillIconsDir, fileName);

            // Download the icon
            Logger.log("Downloading missing icon for " + skill.name());
            if (downloadImage(url, iconFile)) {
              downloaded++;

              // Load the newly downloaded icon
              try {
                BufferedImage icon = ImageIO.read(iconFile);
                if (icon != null) {
                  BufferedImage resized = resizeImage(icon);
                  // Update the icon in the main thread to avoid concurrency issues
                  SwingUtilities.invokeLater(() -> {
                    skillIcons.put(skill, resized);
                  });
                }
              } catch (IOException e) {
                Logger.log("Error loading downloaded icon: " + e.getMessage());
              }
            }

            // Sleep briefly between downloads
            Thread.sleep(100);
          }
        }

        Logger.log("Downloaded " + downloaded + " missing skill icons");
      } catch (Exception e) {
        Logger.log("Error downloading missing icons: " + e.getMessage());
      }
    }).start();
  }

  // Helper method to resize an image to 32x32 with better performance
  private BufferedImage resizeImage(BufferedImage original) {
    // Reduce size from 48 to 32 for better performance
    int size = 32;
    BufferedImage resized = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
    Graphics2D g = resized.createGraphics();

    // Use faster rendering hints
    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);

    g.drawImage(original, 0, 0, size, size, null);
    g.dispose();
    return resized;
  }

  // Helper method to download an image from a URL to a file with better performance
  private boolean downloadImage(String imageUrl, File destinationFile) {
    try {
      URL url = new URL(imageUrl);
      HttpURLConnection connection = (HttpURLConnection) url.openConnection();
      connection.setRequestProperty("User-Agent", "Mozilla/5.0");
      connection.setConnectTimeout(5000);
      connection.setReadTimeout(5000);

      // Create parent directories if they don't exist
      if (!destinationFile.getParentFile().exists()) {
        destinationFile.getParentFile().mkdirs();
      }

      // Use NIO for better performance
      try (InputStream in = new BufferedInputStream(connection.getInputStream());
           FileOutputStream out = new FileOutputStream(destinationFile)) {
        // Larger buffer for better performance
        byte[] buffer = new byte[8192];
        int length;
        while ((length = in.read(buffer)) > 0) {
          out.write(buffer, 0, length);
        }

        Logger.log("Successfully downloaded image to " + destinationFile.getAbsolutePath());
      }
      return true;
    } catch (IOException e) {
      Logger.log("Error downloading image from " + imageUrl + ": " + e.getMessage());
      return false;
    }
  }

  // Helper method to load an image from a URL
  private BufferedImage loadImage(String imageUrl) {
    try {
      URL url = new URL(imageUrl);
      HttpURLConnection connection = (HttpURLConnection) url.openConnection();
      connection.setRequestProperty("User-Agent", "Mozilla/5.0");
      connection.setConnectTimeout(3000);
      connection.setReadTimeout(3000);

      try (InputStream in = new BufferedInputStream(connection.getInputStream())) {
        return ImageIO.read(in);
      }
    } catch (IOException e) {
      Logger.log("Error loading image from URL: " + imageUrl + " - " + e.getMessage());
      return null;
    }
  }

  /**
   * Serializable wrapper for BufferedImage since BufferedImage is not serializable
   */
  static class SerializableBufferedImage implements Serializable {
    private static final long serialVersionUID = 2L; // Increment version to avoid issues with old cache
    private transient BufferedImage image;

    public SerializableBufferedImage(BufferedImage image) {
      this.image = image;
    }

    public BufferedImage getImage() {
      return image;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
      out.defaultWriteObject();

      if (image == null) {
        // Write zeros to indicate null image
        out.writeInt(0);
        out.writeInt(0);
        out.writeInt(0);
        return;
      }

      try {
        // Write image dimensions
        out.writeInt(image.getWidth());
        out.writeInt(image.getHeight());
        out.writeInt(image.getType());

        // Write image data more efficiently using a byte array
        int width = image.getWidth();
        int height = image.getHeight();
        int[] pixels = new int[width * height];
        image.getRGB(0, 0, width, height, pixels, 0, width);

        // Write all pixels at once
        for (int pixel : pixels) {
          out.writeInt(pixel);
        }

        Logger.log("Serialized image: " + width + "x" + height);
      } catch (Exception e) {
        Logger.log("Error serializing image: " + e.getMessage());
        // Write zeros to ensure the stream remains valid
        out.writeInt(0);
        out.writeInt(0);
        out.writeInt(0);
      }
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
      in.defaultReadObject();

      try {
        // Read image dimensions
        int width = in.readInt();
        int height = in.readInt();
        int type = in.readInt();

        if (width <= 0 || height <= 0) {
          Logger.log("Invalid image dimensions: " + width + "x" + height);
          image = null;
          return;
        }

        // Create new image
        image = new BufferedImage(width, height, type);

        // Read image data more efficiently
        int[] pixels = new int[width * height];
        for (int i = 0; i < pixels.length; i++) {
          pixels[i] = in.readInt();
        }

        // Set all pixels at once
        image.setRGB(0, 0, width, height, pixels, 0, width);

        Logger.log("Deserialized image: " + width + "x" + height);
      } catch (Exception e) {
        Logger.log("Error deserializing image: " + e.getMessage());
        image = null;
      }
    }
  }

  // Create a simple fallback icon for total level
  private BufferedImage createFallbackTotalLevelIcon() {
    try {
      // Create a new 32x32 image with transparency
      BufferedImage icon = new BufferedImage(32, 32, BufferedImage.TYPE_INT_ARGB);
      Graphics2D g = icon.createGraphics();

      // Set rendering hints for better quality
      g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
      g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

      // Draw a circular background
      g.setColor(new Color(50, 50, 50));
      g.fillOval(0, 0, 32, 32);

      // Draw a border
      g.setColor(new Color(200, 200, 200));
      g.setStroke(new BasicStroke(2));
      g.drawOval(1, 1, 29, 29);

      // Draw "TTL" text
      g.setColor(Color.WHITE);
      g.setFont(new Font("Arial", Font.BOLD, 12));
      FontMetrics fm = g.getFontMetrics();
      int textWidth = fm.stringWidth("TTL");
      int textHeight = fm.getHeight();
      g.drawString("TTL", (32 - textWidth) / 2, 16 + textHeight / 4);

      g.dispose();
      return icon;
    } catch (Exception e) {
      Logger.log("Error creating fallback icon: " + e.getMessage());
      return null;
    }
  }

  /**
   * Stops the script gracefully
   */
  public static void stopScript() {
    running = false;
    Logger.log("Script stopping gracefully...");
  }

  public void onPaint(Graphics2D graphics) {
    // Always draw mouse cursor and trail regardless of paint visibility
    drawMouseUtil.drawPlusMouse(graphics);

    // Skip the rest of the paint if we're not logged in to save resources
    if (!Client.getGameState().equals(GameState.LOGGED_IN) || !Players.getLocal().isOnScreen()) {
      return;
    }

    // Get the chat box widget
    Widget dialogueBox = Widgets.getWidget(162);
    if (dialogueBox == null) return;
    WidgetChild chatBox = dialogueBox.getChild(34);
    if (chatBox == null) return;

    //Logger.log("Chatbox Sizing: " + chatBox.getWidth() + "x" + chatBox.getHeight());


    // Initialize buffer if needed or if canvas size changed
    if (paintBuffer == null ||
        paintBuffer.getWidth() != Client.getCanvas().getWidth() ||
        paintBuffer.getHeight() != Client.getCanvas().getHeight()) {
      paintBuffer = new BufferedImage(
          Client.getCanvas().getWidth(),
          Client.getCanvas().getHeight(),
          BufferedImage.TYPE_INT_ARGB);
    }

    // Only update the buffer at the specified refresh rate - increased for better performance
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastPaintTime >= 250) { // Update 4 times per second instead of 10
      lastPaintTime = currentTime;

      // Get graphics from buffer
      Graphics2D bufferGraphics = paintBuffer.createGraphics();

      // Clear the buffer with a transparent color
      bufferGraphics.setComposite(AlphaComposite.Clear);
      bufferGraphics.fillRect(0, 0, paintBuffer.getWidth(), paintBuffer.getHeight());
      bufferGraphics.setComposite(AlphaComposite.SrcOver);

      // Set rendering hints - optimized for better performance
      bufferGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
      bufferGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
      bufferGraphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

      // Calculate panel dimensions
      int panelWidth = chatBox.getWidth();
      int panelHeight = chatBox.getHeight();
      int panelX = chatBox.getX();
      int panelY = chatBox.getY();

      // Calculate totals for level and XP gains - cache these calculations
      int totalGainedLevels = 0;
      int totalGainedXP = 0;
      for (Skill skill : targetSkills.keySet()) {
        totalGainedLevels += SkillTracker.getGainedLevels(skill);
        totalGainedXP += SkillTracker.getGainedXP(skill);
      }

      if (afkEnabled && !afkTimer.equals(KazeUtils.formatTime(0))) {
        drawShadowText(
            bufferGraphics, Color.YELLOW, "AFK: " + afkTimer, chatBox.getX() + 10, chatBox.getY() - 20);
      } else if (antibanEnabled && !AntiBanAction.isEmpty()) {
        // Only show Anti-Ban when there's an actual action
        drawShadowText(bufferGraphics, Color.YELLOW, "Anti-Ban: " + AntiBanAction, chatBox.getX() + 10, chatBox.getY() - 30);
      }

      // ===== MAIN PANEL =====
      // Semi-transparent black rectangle for background - simplified for performance
      bufferGraphics.setColor(new Color(0, 0, 0, 180));
      bufferGraphics.fillRect(panelX, panelY, panelWidth, panelHeight);

      // Add a simple border
      bufferGraphics.setColor(new Color(70, 70, 70, 200));
      bufferGraphics.setStroke(new BasicStroke(1));
      bufferGraphics.drawRect(panelX, panelY, panelWidth, panelHeight);

      // Draw title (no rainbow)
      bufferGraphics.setFont(new Font("Arial", Font.BOLD, 16));
      FontMetrics metrics = bufferGraphics.getFontMetrics();
      int textWidth = metrics.stringWidth(text);

      // Center the title
      int x = panelX + (panelWidth / 2) - (textWidth / 2);
      int y = panelY + 25;

      // Draw title shadow and text
      bufferGraphics.setColor(Color.BLACK);
      bufferGraphics.drawString(text, x + 1, y + 1);
      bufferGraphics.setColor(Color.WHITE);
      bufferGraphics.drawString(text, x, y);

      // Draw horizontal divider below title
      bufferGraphics.setColor(new Color(100, 100, 100, 200));
      bufferGraphics.drawLine(panelX + 10, y + 10, panelX + panelWidth - 10, y + 10);

      // ===== REORGANIZED LAYOUT: STATUS INFO LEFT, SKILLS RIGHT =====
      bufferGraphics.setFont(new Font("Arial", Font.PLAIN, 12));
      int contentY = y + 35;
      int leftColumnX = panelX + 10;
      int rightColumnX = panelX + (panelWidth / 2) + 10; // Start skills at middle of panel
      int lineHeight = 20;

      // ===== LEFT SECTION: STATUS INFORMATION =====
      int statusY = contentY;

      // Runtime and Task info
      drawShadowText(bufferGraphics, Color.WHITE, "Runtime:", leftColumnX, statusY);
      drawShadowText(bufferGraphics, Color.YELLOW, ScriptTimer.getElapsedTime(), leftColumnX + 70, statusY);
      statusY += lineHeight;

      drawShadowText(bufferGraphics, Color.WHITE, "Task:", leftColumnX, statusY);
      drawShadowText(bufferGraphics, Color.YELLOW, currentTask, leftColumnX + 70, statusY);
      statusY += lineHeight;

      if (taskTimeRemaining != null && !taskTimeRemaining.isEmpty()) {
        drawShadowText(bufferGraphics, Color.WHITE, "Time left:", leftColumnX, statusY);
        drawShadowText(bufferGraphics, Color.YELLOW, taskTimeRemaining, leftColumnX + 70, statusY);
        statusY += lineHeight;
      }

      drawShadowText(bufferGraphics, Color.WHITE, "Action:", leftColumnX, statusY);
      drawShadowText(bufferGraphics, Color.YELLOW, action, leftColumnX + 70, statusY);

      // ===== RIGHT SECTION: SKILLS =====
      // Draw a vertical divider between sections
      bufferGraphics.setColor(new Color(100, 100, 100, 200));
      bufferGraphics.drawLine(panelX + (panelWidth / 2), contentY - 10,
                             panelX + (panelWidth / 2), panelY + panelHeight - 20);

      // Draw skills in a grid to the right - only draw F2P skills
      List<Skill> f2pSkills = Arrays.asList(
          Skill.ATTACK, Skill.STRENGTH, Skill.DEFENCE,
          Skill.RANGED, Skill.PRAYER, Skill.MAGIC,
          Skill.HITPOINTS, Skill.MINING, Skill.FISHING,
          Skill.WOODCUTTING, Skill.SMITHING, Skill.CRAFTING,
          Skill.COOKING, Skill.FIREMAKING, Skill.RUNECRAFTING
      );

      int skillsPerRow = 4; // Fewer skills per row for better visibility
      int iconSize = 18; // Consistent icon size
      int horizontalSpacing = ((panelWidth / 2) - 15) / skillsPerRow;
      int verticalSpacing = 24; // Increased spacing for better readability

      bufferGraphics.setFont(new Font("Arial", Font.PLAIN, 11));

      // Update cached stats values
      updateCachedStats();

      // Use cached values instead of recalculating every frame
      int currentTotalLevel = cachedTotalLevel;
      int gainedTotalLevels = cachedGainedTotalLevels;

      // Pre-calculate the maximum visible row to avoid drawing off-screen skills
      int maxVisibleRow = (panelY + panelHeight - contentY) / verticalSpacing;

      // Reuse these variables to reduce object creation
      int row, col, skillX, skillY, currentLevel, gainedLevels;
      boolean isEnabled;
      Color skillColor = Color.LIGHT_GRAY; // Default color
      BufferedImage icon;
      String levelText;
      Skill skill;

      // Show all F2P skills - only draw skills that are visible
      int skillCount = f2pSkills.size();
      for (int i = 0; i < skillCount; i++) {
        row = i / skillsPerRow;

        // Skip entire rows that would be off-screen
        if (row > maxVisibleRow) continue;

        col = i % skillsPerRow;
        skillX = rightColumnX + col * horizontalSpacing;
        skillY = contentY + row * verticalSpacing;

        skill = f2pSkills.get(i);

        // Get skill data - only calculate what's needed
        currentLevel = Skills.getRealLevel(skill);
        gainedLevels = SkillTracker.getGainedLevels(skill);
        isEnabled = targetSkills.containsKey(skill);

        // Determine color based on status - reuse the color object
        if (gainedLevels > 0) {
          skillColor = Color.GREEN;
        } else if (isEnabled) {
          skillColor = Color.YELLOW;
        } else {
          skillColor = Color.LIGHT_GRAY;
        }

        // Draw skill icon if available
        icon = skillIcons.get(skill);
        if (icon != null) {
          // Draw icon at consistent position
          bufferGraphics.drawImage(icon, skillX, skillY - iconSize, iconSize, iconSize, null);
        }

        // Format level text - avoid string concatenation when possible
        if (gainedLevels > 0) {
          levelText = currentLevel + " (+" + gainedLevels + ")";
        } else {
          levelText = String.valueOf(currentLevel);
        }

        // Draw level text aligned with icon
        drawShadowText(bufferGraphics, skillColor, levelText, skillX + iconSize + 5, skillY - 5);
      }

      // Add Total Level at the end - place it in the empty space on the last row
      // Calculate the last row and the next available column - reuse variables
      row = f2pSkills.size() / skillsPerRow;
      col = f2pSkills.size() % skillsPerRow;

      // Calculate the position - reuse variables
      skillX = rightColumnX + col * horizontalSpacing;
      skillY = contentY + row * verticalSpacing;

      // Only draw TTL if the row would be visible
      if (row <= maxVisibleRow) {
        // Draw TTL text with icon - reuse variables to reduce object creation
        if (totalLevelIcon != null) {
          if (currentTotalLevel > 999) {
            bufferGraphics.drawImage(totalLevelIcon, skillX - 15, skillY - iconSize, iconSize, iconSize, null);
          } else {
            bufferGraphics.drawImage(totalLevelIcon, skillX, skillY - iconSize, iconSize, iconSize, null);
          }
        } else {
          // If total level icon is not available, draw a text label instead
          bufferGraphics.setColor(Color.yellow);
          bufferGraphics.setFont(new Font("Arial", Font.BOLD, 9));
          bufferGraphics.drawString("TTL", skillX, skillY - 5);
        }

        // Determine color based on gained levels - reuse color object
        if (gainedTotalLevels > 0) {
          skillColor = Color.GREEN;
        } else {
          skillColor = Color.yellow;
        }

        // Format TTL text - avoid string concatenation when possible
        if (gainedTotalLevels > 0) {
          levelText = currentTotalLevel + " (+" + gainedTotalLevels + ")";
        } else {
          levelText = String.valueOf(currentTotalLevel);
        }

        // Draw TTL text
        if (currentTotalLevel > 999) {
          drawShadowText(bufferGraphics, skillColor, levelText, skillX + iconSize - 10, skillY - 5);
        } else {
          drawShadowText(bufferGraphics, skillColor, levelText, skillX + iconSize + 5, skillY - 5);
        }
      } else {
        // Log if TTL would be off-screen - only log occasionally to reduce overhead
        if (currentTime - lastPaintTime >= 10000) { // Reduced logging frequency to 10 seconds
          Logger.log("TTL would be off-screen: Row=" + row + ", MaxVisibleRow=" + maxVisibleRow);
        }
      }

      // Draw a very basic Skip Task button above the chat box on the right
      int buttonWidth = 80;
      int buttonHeight = 20;
      int buttonX = panelX + panelWidth - buttonWidth - 10; // Position on the right side
      int buttonY = panelY - buttonHeight - 5; // Position above the chat box

      // Update button rectangle for click detection
      skipTaskButton.setBounds(buttonX, buttonY, buttonWidth, buttonHeight);

      // Draw simple button
      bufferGraphics.setColor(Color.DARK_GRAY);
      bufferGraphics.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

      // Draw button border
      bufferGraphics.setColor(Color.BLACK);
      bufferGraphics.drawRect(buttonX, buttonY, buttonWidth, buttonHeight);

      // Draw button text
      bufferGraphics.setFont(new Font("Arial", Font.BOLD, 11));
      bufferGraphics.setColor(Color.WHITE);
      bufferGraphics.drawString("Skip Task", buttonX + 15, buttonY + 14);

      // Update task time remaining for real-time display
      updateTaskTimeRemaining();

      // Dispose of the buffer graphics context properly
      bufferGraphics.dispose();
    }

    // Draw the buffer to the screen with proper compositing
    if (paintBuffer != null) {
      // Use SrcOver composite to properly blend with existing content
      Composite originalComposite = graphics.getComposite();
      graphics.setComposite(AlphaComposite.SrcOver);
      graphics.drawImage(paintBuffer, 0, 0, null);
      graphics.setComposite(originalComposite);
    }
  }

  // Helper method for centered text with shadow - optimized to reduce color changes
  private void drawCenteredShadowText(Graphics2D g, Color color, String text, int centerX, int y) {
    FontMetrics fm = g.getFontMetrics();
    int textWidth = fm.stringWidth(text);
    int x = centerX - (textWidth / 2);

    // Draw shadow first
    g.setColor(Color.BLACK);
    g.drawString(text, x + 1, y + 1);

    // Then draw main text
    g.setColor(color);
    g.drawString(text, x, y);
  }

  // Static shadow color to avoid creating new objects
  private static final Color SHADOW_COLOR = new Color(0, 0, 0);

  // Optimized shadow text method to reduce object creation
  private void drawShadowText(Graphics2D g, Color color, String text, int x, int y) {
    // Draw shadow first
    g.setColor(SHADOW_COLOR);
    g.drawString(text, x + 1, y + 1);

    // Then draw main text
    g.setColor(color);
    g.drawString(text, x, y);
  }

  private BufferedImage getSkillIcon(Skill skill) {
    switch (skill) {
      case ATTACK: return attackIcon;
      case STRENGTH: return strengthIcon;
      case DEFENCE: return defenceIcon;
      case RANGED: return rangedIcon;
      case PRAYER: return prayerIcon;
      case MAGIC: return magicIcon;
      case HITPOINTS: return hitpointsIcon;
      case MINING: return miningIcon;
      case FISHING: return fishingIcon;
      case WOODCUTTING: return woodcuttingIcon;
      case FARMING: return farmingIcon;
      case HUNTER: return hunterIcon;
      case SMITHING: return smithingIcon;
      case CRAFTING: return craftingIcon;
      case FLETCHING: return fletchingIcon;
      case COOKING: return cookingIcon;
      case FIREMAKING: return firemakingIcon;
      case HERBLORE: return herbloreIcon;
      case AGILITY: return agilityIcon;
      case THIEVING: return thievingIcon;
      case SLAYER: return slayerIcon;
      case RUNECRAFTING: return runecraftIcon;
      case CONSTRUCTION: return constructionIcon;
      default: return null;
    }
  }

  // Add this method to update the time remaining in real-time
  private void updateTaskTimeRemaining() {
    if (currentActiveTask != null) {
      // If this is a new task or we switched tasks
      if (lastActiveTask != currentActiveTask) {
        int durationMinutes = currentActiveTask.getDurationMinutes();
        long startTime = currentActiveTask.getStartTime();

        // Calculate the end time based on the task's start time and duration
        if (startTime > 0 && durationMinutes > 0) {
          taskEndTime = startTime + (durationMinutes * 60 * 1000L);
        } else {
          taskEndTime = 0;
        }

        lastActiveTask = currentActiveTask;
      }

      // If we have a valid end time, calculate remaining time
      if (taskEndTime > 0) {
        long currentTime = System.currentTimeMillis();
        long remainingMillis = taskEndTime - currentTime;

        if (remainingMillis > 0) {
          // Convert to hours, minutes, seconds
          int totalSeconds = (int)(remainingMillis / 1000);
          int hours = totalSeconds / 3600;
          int minutes = (totalSeconds % 3600) / 60;
          int seconds = totalSeconds % 60;

          // Format with leading zeros
          taskTimeRemaining = String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
          // Time's up
          taskTimeRemaining = "00:00:00";
        }
      }
    } else {
      // No active task
      taskTimeRemaining = "";
      lastActiveTask = null;
    }
  }

  /**
   * Updates cached stats values to reduce calculations during painting
   * Only updates at the specified interval to reduce CPU usage
   */
  private void updateCachedStats() {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastStatsUpdateTime >= STATS_UPDATE_RATE) {
      lastStatsUpdateTime = currentTime;

      // Update total level
      cachedTotalLevel = Skills.getTotalLevel();

      // Update gained levels
      int gainedLevels = 0;
      for (Skill skill : Skill.values()) {
        gainedLevels += SkillTracker.getGainedLevels(skill);
      }
      cachedGainedTotalLevels = gainedLevels;
    }
  }

  @Override
  public void onMessage(Message message) {
    // Handle chat messages here
  }

  @Override
  public void mouseClicked(MouseEvent e) {
    // Check if the skip task button was clicked
    if (skipTaskButton.contains(e.getPoint())) {
      // Set the cooldown timestamp
      lastSkipTaskTime = System.currentTimeMillis();

      // Skip the current task
      skipCurrentTask();

      // Log the click
      Logger.log("Skip Task button clicked");
    }
  }

  @Override
  public void mousePressed(MouseEvent e) {
    // Not needed
  }

  @Override
  public void mouseReleased(MouseEvent e) {
    // Not needed
  }

  @Override
  public void mouseEntered(MouseEvent e) {
    // Not needed
  }

  @Override
  public void mouseExited(MouseEvent e) {
    // Not needed
  }

  /**
   * Skips the current task and selects a new one
   */
  private void skipCurrentTask() {
    if (currentActiveTask != null) {
      Logger.log("Skipping current task: " + currentActiveTask.getName());

      // Store the task name for logging
      String taskName = currentActiveTask.getName();

      // Reset the timer for the current task
      currentActiveTask.resetTimer();

      // Mark the task as completed for this session
      currentActiveTask.markAsCompleted();

      // Clear the current active task so a new one will be selected
      currentActiveTask = null;

      // Reset any task-specific state variables
      resetTaskState();

      // Update the action text
      action = "Skipped task: " + taskName;

      // Force immediate task selection on next loop
      forceTaskSelection = true;
    } else {
      Logger.log("No active task to skip");
    }
  }

  /**
   * Resets all task-specific state variables when switching tasks
   * This ensures clean transitions between tasks
   */
  private void resetTaskState() {
    // Reset any task-specific variables in the Main class
    // Currently, most state is managed within the task classes themselves

    // Clear the cached task list to force a rebuild
    cachedTaskList = null;

    // Reset the action text
    action = "Switching tasks...";

    Logger.log("Reset task state for clean transition");
  }

  // Cache client settings checks to avoid repeated API calls
  private static long lastClientSettingsCheck = 0;

  @Override
  public int onLoop() {
    // Only check client settings every 30 seconds to reduce API calls
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastClientSettingsCheck > 30000) {
      if (ClientSettings.getClientLayout() != RESIZABLE_CLASSIC) {
        ClientSettings.setClientLayout(RESIZABLE_CLASSIC);
        Sleep.sleepUntil(() -> ClientSettings.getClientLayout() == RESIZABLE_CLASSIC, 3000);
      }
      if (!ClientSettings.areRoofsHidden()) {
        ClientSettings.toggleRoofs(false);
        Sleep.sleepUntil(() -> ClientSettings.areRoofsHidden(), 3000);
      }
      if (ClientSettings.isLevelUpInterfaceEnabled()) {
        ClientSettings.toggleLevelUpInterface(false);
        Sleep.sleepUntil(() -> !ClientSettings.isLevelUpInterfaceEnabled(), 3000);
      }
      if (!ClientSettings.isShiftClickDroppingEnabled()) {
        ClientSettings.toggleShiftClickDropping(true);
        Sleep.sleepUntil(() -> ClientSettings.isShiftClickDroppingEnabled(), 3000);
      }
      lastClientSettingsCheck = currentTime;
    }

    // If the bot is not running, just wait
    if (!running) {
      return 1000; // Wait 1 second before checking again
    }

    // Call anti-ban periodically
    KazeUtils.callantiBan();

    // Cache the task list to avoid recreating it every loop
    // Only recreate if we don't have a current active task
    if (cachedTaskList == null) {
      cachedTaskList = new ArrayList<>();
    }

    // If we have a current active task and it's still valid, continue with it
    // Skip this check if forceTaskSelection is true (task was manually skipped)
    if (!forceTaskSelection && currentActiveTask != null) {
      // Cache skill check to avoid repeated map lookups
      Skill currentTaskSkill = currentActiveTask.getSkill();
      boolean skillEnabled = targetSkills.containsKey(currentTaskSkill);

      if (skillEnabled &&
          currentActiveTask.accept() &&
          !currentActiveTask.hasReachedDuration()) {
        return currentActiveTask.execute();
      } else if (!skillEnabled) {
        // Current task's skill is no longer enabled, reset current task
        Logger.log("Current task " + currentActiveTask.getName() + " is no longer valid (skill " + currentTaskSkill.name() + " disabled). Selecting new task.");
        currentActiveTask = null;
      }
    }

    // Reset the force selection flag
    if (forceTaskSelection) {
      forceTaskSelection = false;
      Logger.log("Forcing task selection after skip");
    }

    // Check if we need to rebuild the task list (every 10 seconds)
    boolean rebuildTaskList = cachedTaskList.isEmpty() ||
                             System.currentTimeMillis() - lastTaskListUpdateTime > 10000;

    if (rebuildTaskList) {
      // Basic validation checks
      if (selectedTasks == null || selectedTasks.isEmpty()) {
        Logger.log("No tasks selected. Please configure the bot through the GUI.");
        return 1000;
      }

      if (targetSkills == null || targetSkills.isEmpty()) {
        Logger.log("No target skills set. Please configure the bot through the GUI.");
        return 1000;
      }

      // Convert selectedTasks from ArrayList<Object> to List<AbstractTask>
      cachedTaskList = new ArrayList<>();
      for (Object obj : selectedTasks) {
        AbstractTask task = null;

        if (obj instanceof AbstractTask) {
          task = (AbstractTask) obj;
        } else if (obj instanceof String) {
          // Try to find task by name
          task = TaskManager.getTaskByName((String) obj);
        }

        // Only add tasks whose skills are enabled
        if (task != null) {
          Skill taskSkill = task.getSkill();
          if (targetSkills.containsKey(taskSkill)) {
            cachedTaskList.add(task);
          }
        }
      }

      lastTaskListUpdateTime = System.currentTimeMillis();

      if (cachedTaskList.isEmpty()) {
        Logger.log("No valid tasks found in selected tasks list. Attempting to auto-select tasks...");

        // Try to auto-select tasks for enabled skills
        boolean autoSelectedTasks = false;
        if (selectedTasks == null) {
          selectedTasks = new ArrayList<>();
        }

        for (Skill skill : targetSkills.keySet()) {
          List<AbstractTask> availableTasks = TaskManager.getTasksForSkill(skill);
          if (!availableTasks.isEmpty()) {
            // Add ALL available tasks for this skill to give more options
            for (AbstractTask task : availableTasks) {
              // Check if task is not already in the list
              boolean alreadyExists = selectedTasks.stream().anyMatch(existing -> {
                if (existing instanceof AbstractTask) {
                  return ((AbstractTask) existing).getName().equals(task.getName());
                } else if (existing instanceof String) {
                  return existing.equals(task.getName());
                }
                return false;
              });

              if (!alreadyExists) {
                selectedTasks.add(task);
                cachedTaskList.add(task);
                autoSelectedTasks = true;
                Logger.log("Auto-selected task: " + task.getName() + " for skill: " + skill.name());
              }
            }
          }
        }

        if (!autoSelectedTasks) {
          Logger.log("No tasks available for enabled skills. Please check your configuration.");
          return 1000;
        } else {
          Logger.log("Auto-selected " + cachedTaskList.size() + " tasks. Bot will continue.");
        }
      }
    }

    // Select the best task to execute
    AbstractTask taskToExecute = TaskManager.selectBestFromSelectedTasks(cachedTaskList, targetSkills);

    if (taskToExecute == null) {
      // The TaskManager will have already set appropriate messages
      // and started the shutdown process if all tasks are completed
      // Just wait for the next loop
      return 1000;
    }

    // Update current task name and time remaining
    currentTask = taskToExecute.getName();

    // Update time remaining if this is the current active task
    if (currentActiveTask != null && currentActiveTask.equals(taskToExecute)) {
      int remainingMinutes = currentActiveTask.getRemainingMinutes();

      // Format as hours and minutes
      if (remainingMinutes >= 60) {
        int hours = remainingMinutes / 60;
        int minutes = remainingMinutes % 60;
        taskTimeRemaining = hours + "H" + minutes + "M";
      } else {
        taskTimeRemaining = remainingMinutes + "M";
      }
    } else {
      taskTimeRemaining = "";
    }

    // Check if task has required items - cache the result to avoid repeated calls
    boolean hasRequiredItems = taskToExecute.hasRequiredItems();
    if (hasRequiredItems) {
      // Check if we already have all required items
      if (!BankingLogic.hasRequiredItems(taskToExecute)) {
        // Use the BankingLogic class to handle banking for this task
        boolean bankingSuccessful = BankingLogic.handleBanking(taskToExecute);
        if (!bankingSuccessful) {
          if (Main.DEBUG) {
            Logger.log("Failed to handle banking for task: " + currentTask);
          }
          // Mark this task as failed so we don't select it again for a while
          taskToExecute.markAsFailed();

          // Skip this task for now
          if (currentActiveTask == taskToExecute) {
            currentActiveTask = null; // Reset current task so we can select a new one
          }
          return 1000;
        }
      }
    }

    // Final safety check: Ensure we're only executing tasks for enabled skills
    Skill taskSkill = taskToExecute.getSkill();
    if (!targetSkills.containsKey(taskSkill)) {
      Logger.log("SAFETY CHECK FAILED: Attempting to execute task " + taskToExecute.getName() +
                 " for disabled skill " + taskSkill.name() + ". Skipping task.");

      // Mark this task as failed and reset current task
      taskToExecute.markAsFailed();
      if (currentActiveTask == taskToExecute) {
        currentActiveTask = null;
      }

      // Force task reselection
      forceTaskSelection = true;
      return 1000;
    }

    try {
      return taskToExecute.execute();
    } catch (Exception e) {
      Logger.log("Error executing task: " + e.getMessage());
      e.printStackTrace();
      return 1000;
    }
  }

}
