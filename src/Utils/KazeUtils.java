package Utils;

import static org.dreambot.api.utilities.Sleep.sleep;

import Main.Main;
import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.dreambot.api.Client;
import org.dreambot.api.input.Mouse;
import org.dreambot.api.methods.Calculations;
import org.dreambot.api.methods.combat.Combat;
import org.dreambot.api.methods.combat.CombatStyle;
import org.dreambot.api.methods.container.impl.Inventory;
import org.dreambot.api.methods.container.impl.bank.Bank;
import org.dreambot.api.methods.container.impl.bank.BankLocation;
import org.dreambot.api.methods.container.impl.bank.BankMode;
import org.dreambot.api.methods.container.impl.equipment.Equipment;
import org.dreambot.api.methods.grandexchange.GrandExchange;
import org.dreambot.api.methods.input.Camera;
import org.dreambot.api.methods.interactive.GameObjects;
import org.dreambot.api.methods.interactive.NPCs;
import org.dreambot.api.methods.interactive.Players;
import org.dreambot.api.methods.item.GroundItems;
import org.dreambot.api.methods.map.Tile;
import org.dreambot.api.methods.settings.PlayerSettings;
import org.dreambot.api.methods.tabs.Tab;
import org.dreambot.api.methods.tabs.Tabs;
import org.dreambot.api.methods.walking.impl.Walking;
import org.dreambot.api.methods.widget.Widgets;
import org.dreambot.api.methods.world.Worlds;
import org.dreambot.api.methods.worldhopper.WorldHopper;
import org.dreambot.api.script.ScriptManager;
import org.dreambot.api.utilities.Logger;
import org.dreambot.api.utilities.Sleep;
import org.dreambot.api.wrappers.interactive.GameObject;
import org.dreambot.api.wrappers.interactive.NPC;
import org.dreambot.api.wrappers.interactive.Player;
import org.dreambot.api.wrappers.items.GroundItem;
import org.dreambot.api.wrappers.items.Item;

public class KazeUtils {
  public static final int RANDOM_SLEEP_TIME = Calculations.random(200, 600);
  private static final Random random = new Random(); // Initialize Java Random
  private static final long antiBanCooldown = 30000;
  private static final long MINIMUM_RUNTIME_BEFORE_LONG_SLEEP = 10 * 60 * 1200000; // 120 minutes in milliseconds
  private static long lastAntiBanTime = 0;
  private static long scriptStartTime = System.currentTimeMillis(); // Add this at the class level

  // Track last time we logged combat status to reduce spam
  private static long lastCombatLogTime = 0;
  private static long lastNPCSearchTime = 0;
  public static NPC cachedNearestNPC = null;
  public static Tile cachedNPCTile = null;
  private static String cachedNPCName = "";
  private static int cachedDistance = 0;

  // Add these fields to track killed NPC tiles
  private static final int MAX_KILLED_NPC_TILES = 10; // Keep track of last 10 killed NPCs
  private static final Queue<Tile> killedNPCTiles = new LinkedList<>();
  private static long lastNPCDeathTime = 0;

  // Add this method to record NPC deaths
  private static void recordNPCDeath(NPC npc) {
    if (npc != null && npc.getTile() != null) {
      // Add the tile to our history
      killedNPCTiles.add(npc.getTile());

      // Keep the queue at maximum size
      while (killedNPCTiles.size() > MAX_KILLED_NPC_TILES) {
        killedNPCTiles.poll(); // Remove oldest tile
      }

      lastNPCDeathTime = System.currentTimeMillis();

      if (Main.DEBUG) {
        Logger.log("Recorded NPC death at tile: " + npc.getTile());
      }
    }
  }

  public static boolean attackNPC(int distance, String npcName) {
    // If player is in combat, just update the action text and return
    if (Players.getLocal().isInCombat()) {
      Main.action = "In combat";

      // Only log combat status every 5 seconds to reduce spam
      if (Main.DEBUG && System.currentTimeMillis() - lastCombatLogTime > 5000) {
        Logger.log("In combat with " + npcName);
        lastCombatLogTime = System.currentTimeMillis();
      }

      // Check if we were previously tracking an NPC that's now dead
      if (cachedNearestNPC != null && !cachedNearestNPC.exists()) {
        recordNPCDeath(cachedNearestNPC);
      }

      return true;
    }

    // Check if we already have a valid cached target that we can attack
    NPC nearestNPC;
    if (cachedNearestNPC != null &&
        cachedNearestNPC.exists() &&
        !cachedNearestNPC.isInCombat() &&
        cachedNearestNPC.canReach() &&
        cachedNearestNPC.getName().equals(npcName) &&
        cachedNearestNPC.distance() <= distance) {
      // Use the existing cached target
      nearestNPC = cachedNearestNPC;
    }
    // Only search for a new NPC if cache is expired or invalid
    else if (System.currentTimeMillis() - lastNPCSearchTime >= 600 ||
        !cachedNPCName.equals(npcName) ||
        cachedDistance != distance ||
        cachedNearestNPC == null) {
      // Add a small randomized sleep before selecting a new target
      sleep(Calculations.random(300, 800));

      nearestNPC = NPCs.closest(npc -> npc != null &&
              npc.getName().equals(npcName) &&
              !npc.isInCombat() &&
              npc.canReach() &&
              !isBeingTargetedByOtherPlayer(npc) &&
              npc.distance() <= distance);

      // Update cache
      cachedNearestNPC = nearestNPC;
      cachedNPCName = npcName;
      cachedDistance = distance;
      cachedNPCTile = nearestNPC != null ? nearestNPC.getTile() : null;
      lastNPCSearchTime = System.currentTimeMillis();
    } else {
      // Use existing cached NPC
      nearestNPC = cachedNearestNPC;
    }

    if (nearestNPC != null) {
      Main.action = "Attacking " + npcName;

      if (Main.DEBUG && System.currentTimeMillis() - lastCombatLogTime > 5000) {
        Logger.log("Attacking " + npcName);
        lastCombatLogTime = System.currentTimeMillis();
      }

      if (!nearestNPC.isOnScreen()) {
        Camera.rotateToEntity(nearestNPC);
      }

      KazeMouse.setPerfectAction(true);
      nearestNPC.interact("Attack");
      Sleep.sleepUntil(() -> Players.getLocal().isInCombat(), 3000);
      KazeMouse.setPerfectAction(false);
      return true;
    } else {
      Main.action = "Waiting for " + npcName;
      return false;
    }
  }


  // Cache player targeting checks to reduce CPU usage
  private static long lastPlayerCheckTime = 0;
  private static Map<NPC, Boolean> targetedNPCCache = new HashMap<>();

  private static boolean isBeingTargetedByOtherPlayer(NPC npc) {
    if (npc == null) return false;

    // Clear cache every 2 seconds
    if (System.currentTimeMillis() - lastPlayerCheckTime > 2000) {
      targetedNPCCache.clear();
      lastPlayerCheckTime = System.currentTimeMillis();
    } else if (targetedNPCCache.containsKey(npc)) {
      // Return cached result if available
      return targetedNPCCache.get(npc);
    }

    // Only check players that are very close to the NPC
    List<Player> nearbyPlayers = Players.all(player ->
        player != null &&
        !player.equals(Players.getLocal()) &&
        player.distance(npc) < 2);

    // If no nearby players, we can return quickly
    if (nearbyPlayers.isEmpty()) {
      targetedNPCCache.put(npc, false);
      return false;
    }

    for (Player player : nearbyPlayers) {
      if (player.getInteractingCharacter() != null &&
          player.getInteractingCharacter().equals(npc)) {
        targetedNPCCache.put(npc, true);
        return true;
      }
    }

    targetedNPCCache.put(npc, false);
    return false;
  }
  public static void openNearestBank() {
    BankLocation bankLocation = BankLocation.getNearest();
    if (!Bank.isOpen()) {
      // First verify we're close enough to the bank
      if (bankLocation.getArea(15).contains(Players.getLocal().getTile())) {
        // Try to open the bank
        int openAttempts = 0;
        while (!Bank.isOpen() && openAttempts < 5) {
          if (Bank.open(bankLocation)) {
            // Wait for bank to open with proper verification
            boolean opened = Sleep.sleepUntil(
                    Bank::isOpen, // Condition to check
                    () -> Players.getLocal().isMoving(), // Exit condition if moving
                    500, // Polling interval
                    3000  // Max wait time
            );

            if (Bank.isOpen()) {
              if (Main.DEBUG) {
                Logger.log("Bank opened successfully on attempt " + (openAttempts + 1));
              }
              break;
            }
          }

          // If we're still here, the bank didn't open
          openAttempts++;
          if (Main.DEBUG) {
            Logger.log("Failed to open bank, attempt " + openAttempts);
          }

          // Small delay before trying again
          Sleep.sleep(300, 600);
        }
      } else {
        // Not close enough to bank, walk closer
        if (Main.DEBUG) {
          Logger.log("Not close enough to bank, walking closer");
        }
        walkToBankLocation(bankLocation);
      }
    }
  }
  public static void openBank(BankLocation bankLocation) {
    if (!Bank.isOpen()) {
      // First verify we're close enough to the bank
      if (bankLocation.getArea(15).contains(Players.getLocal().getTile())) {
        // Try to open the bank
        int openAttempts = 0;
        while (!Bank.isOpen() && openAttempts < 5) {
          if (Bank.open(bankLocation)) {
            // Wait for bank to open with proper verification
            boolean opened = Sleep.sleepUntil(
                    Bank::isOpen, // Condition to check
                    () -> Players.getLocal().isMoving(), // Exit condition if moving
                    500, // Polling interval
                    3000  // Max wait time
            );

            if (Bank.isOpen()) {
              if (Main.DEBUG) {
                Logger.log("Bank opened successfully on attempt " + (openAttempts + 1));
              }
              break;
            }
          }

          // If we're still here, the bank didn't open
          openAttempts++;
          if (Main.DEBUG) {
            Logger.log("Failed to open bank, attempt " + openAttempts);
          }

          // Small delay before trying again
          Sleep.sleep(300, 600);
        }
      } else {
        // Not close enough to bank, walk closer
        if (Main.DEBUG) {
          Logger.log("Not close enough to bank location, walking closer");
        }
        walkToBankLocation(bankLocation);
      }
    }
  }
  public static void interactInv(String itemname, String action) {
    Item inventoryitem = Inventory.get(itemname);

    if (!Inventory.isOpen()) {
      int chance = random.nextInt(100);
      if (chance > 40) {
        Tabs.openWithMouse(Tab.INVENTORY);
      } else {
        Tabs.openWithFKey(Tab.INVENTORY);
      }
    }
    if (inventoryitem != null) {
      Main.action = (itemname + " Using " + action);
      if (Main.DEBUG) {
        Logger.log("Interacting with " + itemname + " Using action " + action);
      }
      inventoryitem.interact(action);
      sleep(RANDOM_SLEEP_TIME);
    }
  }
  public static void interactGround(String itemname, String action) {
    GroundItem groundItem = GroundItems.closest(itemname);

    if (groundItem == null) return;

    int InventoryAmount = Inventory.count(itemname);
    int grounditemAmount = groundItem.getAmount();

    // Check if this is our loot by comparing with any of our killed NPC tiles
    boolean isOurLoot = false;
    Tile groundItemTile = groundItem.getTile();

    // First check current cached NPC tile
    if (cachedNPCTile != null && groundItemTile.equals(cachedNPCTile)) {
      isOurLoot = true;
      if (Main.DEBUG) {
        Logger.log("Found our loot at current cached NPC tile: " + itemname);
      }
    }
    // Then check our history of killed NPC tiles
    else {
      for (Tile killedTile : killedNPCTiles) {
        if (groundItemTile.equals(killedTile)) {
          isOurLoot = true;
          if (Main.DEBUG) {
            Logger.log("Found our loot at historical NPC tile: " + itemname);
          }
          break;
        }
      }
    }

    if (!isOurLoot && Main.DEBUG) {
      Logger.log("Found loot but not at any of our NPC tiles: " + itemname);
    }

    // Only pick up if it's our loot or we're not being selective
    if ((isOurLoot || killedNPCTiles.isEmpty()) && groundItem.exists() && groundItem.canReach()) {
      Main.action = (itemname + " Using " + action);
      if (Main.DEBUG) {
        Logger.log("Interacting with " + itemname + " Using " + action);
      }
      KazeMouse.setPerfectAction(true);
      groundItem.interact(action);

      Sleep.sleepUntil(
          () -> (Inventory.count(itemname) == (InventoryAmount + grounditemAmount)), 10000);
      KazeMouse.setPerfectAction(false);
    }
  }
  public static void interactObject(String object, String action) {
    GameObject closestObject = GameObjects.closest(object);
    if (closestObject != null) {
      closestObject.interact(action);
      Main.action = "Interacting with " + object;
      if (Main.DEBUG) {
        Logger.log("Interacting with " + object + " Using action " + action);
      }
    }
  }
  public static boolean interactNPC(String npcName, String action) {
    NPC closestNPC = NPCs.closest(npc -> npc != null &&
                                  npc.getName().equals(npcName) &&
                                  npc.canReach() &&
                                  !isBeingTargetedByOtherPlayer(npc) && !Players.getLocal().isAnimating());
    if (closestNPC == null) {
      return false;
    }

    // Ensure NPC is on screen
    if (!closestNPC.isOnScreen()) {
      Camera.rotateToEntity(closestNPC);
      Sleep.sleep(200, 400);
    }

    // Set action before interaction
    Main.action = "Interacting with " + npcName + " using " + action;

    // Perform interaction with retry logic
    int attempts = 0;
    boolean interacted = false;

    while (attempts < 3 && !interacted) {
      if (closestNPC.interact(action)) {
        if (Main.DEBUG) {
          Logger.log("Interacting with " + npcName + " using " + action);
        }

        // Wait for interaction to begin
        interacted = Sleep.sleepUntil(() ->
            Players.getLocal().isInteracting(closestNPC) ||
            Players.getLocal().isMoving(),
            2000);
      }

      attempts++;
      if (!interacted && attempts < 3) {
        Sleep.sleep(300, 500); // Brief pause between attempts
      }
    }

    return interacted;
  }
  public static void withdrawItem(String item, int amount) {

    if (Bank.getWithdrawMode() != BankMode.ITEM) {
      Bank.setWithdrawMode(BankMode.ITEM);
    }

    if (amount == 1 && !(PlayerSettings.getBitValue(6590) == 0)) {
      Widgets.getWidget(12).getChild(30).interact();
    }
    if (Bank.contains(item)) {
      Main.action = "Withdrawing " + item;
      Bank.withdraw(item, amount);
      if (Main.DEBUG) {
        Logger.log("Withdrawing " + item + "(x" + amount + ") from bank");
      }
      Sleep.sleepUntil(() -> Inventory.contains(item), 10000);
    }
  }
  public static void depositItem(String item, int amount) {
    if (Inventory.contains(item)) {
      Main.action = "Depositing " + item;
      Bank.deposit(item, amount);
      if (Main.DEBUG) {
        Logger.log("Depositing " + item + "(x" + amount + ") to bank");
      }
      Sleep.sleepUntil(() -> !Inventory.contains(item), 10000);
    }
  }
  public static void depositAll() {
    Main.action = "Depositing all items";
    Bank.depositAllItems();
    if (Main.DEBUG) {
      Logger.log("Depositing all items");
    }
    Sleep.sleepUntil(Inventory::isEmpty, 10000);
  }
  /**
   * Withdraws the best available food from the bank based on healing values.
   * @param amount Number of food items to withdraw
   * @return true if food was successfully withdrawn, false otherwise
   */
  public static boolean withdrawBestFood(int amount) {
    if (!Bank.isOpen()) {
      Bank.open();
      Sleep.sleepUntil(Bank::isOpen, 5000);
      return false;
    }

    // Food items ordered by healing value (lowest to highest)
    List<String> foodPriorityList = Arrays.asList(
            // Basic foods
            "Shrimps", "Cooked chicken", "Cooked meat", "Sardine", "Bread", "Herring", "Mackerel",
            "Trout", "Cod", "Pike", "Salmon", "Tuna", "Lobster", "Bass", "Swordfish",
            "Potato with butter", "Monkfish", "Shark", "Sea turtle", "Manta ray", "Dark crab",
            // Special foods
            "Cake", "Chocolate cake", "Pineapple pizza", "Potato with cheese",
            "Tuna potato", "Anglerfish", "Karambwan"
    );

    // First, check if any food is already in the inventory
    for (int i = foodPriorityList.size() - 1; i >= 0; i--) {
      String food = foodPriorityList.get(i);

      if (Inventory.contains(food)) {
        Logger.log("Best food already in inventory: " + food);
        return true;
      }
    }

    // If not in inventory, find the best available food in the bank
    for (int i = foodPriorityList.size() - 1; i >= 0; i--) {
      String food = foodPriorityList.get(i);

      // Retry logic: Attempt to find the food up to 3 times
      int attempts = 0;
      while (attempts < 3) {
        if (Bank.contains(food)) {
          // Determine how many to withdraw (all or specific amount)
          int actualAmount = amount;
          if (amount > Bank.count(food)) {
            actualAmount = Bank.count(food);
          }

          if (Bank.getWithdrawMode() != BankMode.ITEM) {
            Bank.setWithdrawMode(BankMode.ITEM);
          }
          Bank.withdraw(food, actualAmount);

          // Wait for withdrawal to complete
          boolean withdrawn = Sleep.sleepUntil(() -> Inventory.contains(food), 3000);

          if (withdrawn) {
            Logger.log("Successfully withdrew " + actualAmount + "x " + food);
            return true;
          } else {
            Logger.log("Withdrawal attempt failed for: " + food);
          }
        }

        attempts++;
        Sleep.sleep(300, 500); // Small delay before retrying
      }
    }

    Logger.log("No food found in bank. Consider getting some food.");
    return false;
  }
  public static void withdrawAll(String item) {
    if (Bank.getWithdrawMode() != BankMode.ITEM) {
      Bank.setWithdrawMode(BankMode.ITEM);
    }
    Bank.withdrawAll(item);
    Main.action = "Withdrawing all " + item;
    if (Main.DEBUG) {
      Logger.log("Withdrawing all " + item + " from bank");
    }
    Sleep.sleepUntil(() -> !Inventory.isEmpty(), 10000);
  }

  public static void walkToTile(Tile tile) {
    // Walk directly to the specified tile instead of a random tile in the area
    if (!Players.getLocal().isMoving()) {
      Walking.walk(tile);
      Main.action = "Walking to " + tile;
      if (Main.DEBUG) {
        Logger.log("Walking to " + tile);
      }
    }
  }
  /**
   * Walks to the exact tile specified using Walking.walkExact
   * This ensures the character walks to the precise tile rather than a nearby one
   * @param tile The exact tile to walk to
   */
  public static void walkToExactTile(Tile tile) {
    if (!Players.getLocal().isMoving()) {
      Walking.walkExact(tile);
      Main.action = "Walking to exact tile " + tile;
      if (Main.DEBUG) {
        Logger.log("Walking to exact tile " + tile);
      }
    }
  }
  public static void walkToBankLocation(BankLocation location) {
    // Using the enum passed to the method
    if (!location.getArea(5).contains(Players.getLocal().getTile())) {
      // Keep trying to walk to the bank until we reach it
      int walkAttempts = 0;
      while (!location.getArea(5).contains(Players.getLocal().getTile()) && walkAttempts < 20) {
        // Only issue a new walk command if we're not already moving
        if (!Players.getLocal().isMoving() ||
            walkAttempts % 5 == 0) { // Also reissue command every 5 attempts

            Walking.walk(location.getArea(5).getRandomTile());
            Main.action = "Walking to " + location + " (attempt " + (walkAttempts + 1) + ")";
            if (Main.DEBUG) {
              Logger.log("Walking to " + location + " (attempt " + (walkAttempts + 1) + ")");
            }
        }

        // Wait a bit for the player to move
        Sleep.sleep(500, 1000);

        // Check if we've reached the bank
        if (location.getArea(5).contains(Players.getLocal().getTile())) {
            if (Main.DEBUG) {
              Logger.log("Reached bank area");
            }
            break;
        }

        walkAttempts++;
      }
    }
  }
  public static void walkToCoords(int x, int y, int z) {
    if (!Players.getLocal().isMoving()) {
      Walking.walk(x, y, z);
      Main.action = "Walking to Coords";
      if (Main.DEBUG) {
        Logger.log("Walking to Coords");
      }
    }
  }
  public static void swapCombatType(CombatStyle style) {
    if (!Tabs.open(Tab.COMBAT)) {
      Tabs.openWithMouse(Tab.COMBAT);
      Combat.setCombatStyle(style);
      Main.action = "Swapping Combat Style to " + style;
    }
  }
  // Track last time we reset anti-ban message
  private static long lastAntiBanResetTime = 0;

  public static void callantiBan() {
    long currentTime = System.currentTimeMillis();

    // Only reset anti-ban message every 5 seconds to reduce overhead
    if (currentTime - lastAntiBanResetTime > 5000) {
      Main.antibanEnabled = false;
      Main.AntiBanAction = "";
      lastAntiBanResetTime = currentTime;
    }

    // Only check for anti-ban if cooldown has elapsed
    if (currentTime - lastAntiBanTime > antiBanCooldown) {
      // Reduce chance to 20% to decrease frequency
      int chance = 20;
      if (random.nextInt(100) < chance) {
        antiBan();
        lastAntiBanTime = currentTime;
      }
    }
  }
  public static void antiBan() {
    int action = random.nextInt(10) + 1; // Expanded to 10 actions

    switch (action) {
      case 1:
        moveMouseOffScreen();
        break;
      case 2:
        checkInventory();
        break;
      case 3:
        rotateCamera();
        break;
      case 4:
        moveMouseRandomly();
        break;
      case 5:
        moveMouseRandomly();
        break;
      case 6:
        moveMouseOffScreen();
        break;
      case 7:
        adjustZoom();
        break;
      case 8:
        moveMouseRandomly();
        break;
      case 9:
        checkInventory();
        break;
      case 10:
        toggleRun();
        break;
    }
  }
  private static void moveMouseOffScreen() {
    try {
      Thread.sleep(200);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
    Main.antibanEnabled = true;
    Main.AntiBanAction = "Moving mouse off-screen";
    Mouse.moveOutsideScreen(); // Move the mouse off-screen
    Logger.log("Anti-ban: Mouse moved off-screen.");
    Main.antibanEnabled = false;
    Main.AntiBanAction = "";
  }
  private static void rotateCamera() {
    int yaw = random.nextInt(360); // Random yaw between 0-360
    int pitch = random.nextInt(75) + 25; // Random pitch between 25-100 (reasonable pitch range)
    Main.antibanEnabled = true;
    Main.AntiBanAction = "Camera Rotated";
    Camera.rotateTo(yaw, pitch); // Rotate the camera using both yaw and pitch
    Logger.log("Anti-ban: Camera rotated to yaw: " + yaw + ", pitch: " + pitch);
    Main.antibanEnabled = false;
    Main.AntiBanAction = "";
  }
  public static String formatTime(long millis) {
    long hours = TimeUnit.MILLISECONDS.toHours(millis);
    long minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60;
    long seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % 60;

    return String.format("%02d:%02d:%02d", hours, minutes, seconds);
  }
  private static void adjustZoom() {
    // Randomly zoom in or out
    Main.antibanEnabled = true;
    Main.AntiBanAction = "Adjusting Zoom";
    int zoomChange = random.nextInt(20) + 10;
    if (random.nextBoolean()) {
      Camera.setZoom(Camera.getZoom() + zoomChange);
    } else {
      Camera.setZoom(Camera.getZoom() - zoomChange);
    }
    Main.antibanEnabled = false;
    Logger.log("Anti-ban: Adjusted zoom level");
  }
  private static void moveMouseRandomly() {
    // Move mouse to random point on screen
    Main.antibanEnabled = true;
    Main.AntiBanAction = "Random Mouse Movement";

    Point randomPoint =
        new Point(
            random.nextInt(Client.getCanvas().getWidth()),
            random.nextInt(Client.getCanvas().getHeight()));
    Mouse.move(randomPoint);
    Main.antibanEnabled = false;
    sleep(200, 800);
  }
  private static void checkInventory() {
    if (!Tabs.isOpen(Tab.INVENTORY)) {
      Tabs.openWithMouse(Tab.INVENTORY);
      sleep(300, 800);
    }
    // Hover over random inventory item
    List<Item> inventoryItems = Collections.singletonList(Inventory.get(item -> item != null));
    Item randomItem = null;
    if (!inventoryItems.isEmpty()) {
      randomItem = inventoryItems.get(random.nextInt(inventoryItems.size()));
    }
    if (randomItem != null) {
      Main.antibanEnabled = true;
      Main.AntiBanAction = "Checking Inventory";
      sleep(500, 1500);
      // 30% chance to right-click item
      if (random.nextInt(100) < 10) {
        randomItem.interact("Examine");
        sleep(800, 1200);
      }
      Main.antibanEnabled = false;
    }
  }
  private static void toggleRun() {
    // Only toggle if run energy is above 50%
    Main.antibanEnabled = true;
    Main.AntiBanAction = "Toggling Run";
    if (Walking.getRunEnergy() > 50 && !Walking.isRunEnabled()) {
      Walking.toggleRun();
      Logger.log("Anti-ban: Toggled run");
      sleep(500, 1000);
    }
    Main.antibanEnabled = false;
  }
  /**
   * Mines a rock of the specified type
   *
   * @param rockTypes The types of rocks to mine (e.g., "Tin rock", "Copper rock")
   * @return true if successfully mining, false otherwise
   */
  public static boolean mineRock(int distance,String... rockTypes) {
    // Log the rock types we're looking for
    if (Main.DEBUG) {
      Logger.log("Looking for rocks: " + Arrays.toString(rockTypes));
    }

    // Find the nearest rock of the specified types within 3 tiles
    GameObject rock = GameObjects.closest(obj -> {
      if (obj == null) return false;
      if (obj.distance() > distance) return false; // Only look for rocks within 3 tiles

      String name = obj.getName().toLowerCase();
      for (String rockType : rockTypes) {
        if (name.equals(rockType.toLowerCase())) {
          return true;
        }
      }
      return false;
    });

    // If player is already mining, don't interrupt


    // If no rock found, return false
    if (rock == null) {
      if (Main.DEBUG) {
        Logger.log("No matching rocks found within " + distance + " tiles");
      }
      return false;
    } else if (Main.DEBUG) {
      Logger.log("Found rock: " + rock.getName() + " at distance: " + rock.distance());
    }

    // If player is already mining, don't interrupt
    if (Players.getLocal().isAnimating() && !Inventory.isFull()) {
      return true;
    }

    // Mine the rock
    if (rock.interact("Mine")) {
      Main.action = "Mining " + rock.getName();
      if (Main.DEBUG) {
        Logger.log("Interacting with rock: " + rock.getName());
      }

      // Wait until player starts mining or moves to the rock
      boolean success = Sleep.sleepUntil(
              () -> Players.getLocal().isAnimating() || !rock.exists(),
              5000
      );

      if (Main.DEBUG) {
        if (success) {
          Logger.log("Successfully started mining");
        } else {
          Logger.log("Failed to start mining");
        }
      }

      return true;
    } else if (Main.DEBUG) {
      Logger.log("Failed to interact with rock");
    }

    return false;
  }

  public static boolean interactFishingSpot(String Fishingspotname, String action) {
    // First check if player is already fishing
    Player local = Players.getLocal();
    if (local.isAnimating() && local.getAnimation() != -1) {
      // Check if the animation is a fishing animation (common fishing animations)
      int animation = local.getAnimation();
      if (animation == 621 || animation == 622 || animation == 623 ||
          animation == 618 || animation == 619 || animation == 620) {
        // Already fishing, no need to interact
        return true;
      }
    }



    // Find the closest fishing spot
    NPC closestFishingSpot = NPCs.closest(npc -> npc != null &&
                                  npc.getName().equals(Fishingspotname) &&
                                  npc.canReach() &&
                                  !Players.getLocal().isAnimating());



    if (closestFishingSpot == null) {
      if (Main.DEBUG) {
        Logger.log("No fishing spots found nearby");
      }
      return false;
    }

    // Ensure fishing spot is on screen
    if (!closestFishingSpot.isOnScreen()) {
      Camera.rotateToEntity(closestFishingSpot);
      Sleep.sleep(200, 400);
    }

    // Set action before interaction
    Main.action = "Fishing with " + action;

    // Perform interaction with retry logic
    int attempts = 0;
    boolean interacted = false;

    while (attempts < 3 && !interacted) {
      if (closestFishingSpot.interact(action)) {
        if (Main.DEBUG) {
          Logger.log("Interacting with fishing spot using " + action);
        }

        // Wait for fishing animation to begin
        interacted = Sleep.sleepUntil(() ->
            Players.getLocal().isAnimating() ||
            Players.getLocal().isInteracting(closestFishingSpot),
            3000);
      }

      attempts++;
      if (!interacted && attempts < 3) {
        Sleep.sleep(300, 500); // Brief pause between attempts
      }
    }

    return interacted;
  }

  /**
   * Deposits all items except the specified ones
   * @param itemsToKeep Array of item names to keep in inventory
   * @return true if deposit operation was successful, false otherwise
   */
  public static boolean depositAllExcept(String... itemsToKeep) {
    if (!Bank.isOpen()) {
      return false;
    }

    // Create a list of items to keep for easier checking
    List<String> keepList = Arrays.asList(itemsToKeep);

    // First check if we need to deposit anything
    boolean needToDeposit = false;
    for (Item item : Inventory.all()) {
      if (item != null && !keepList.contains(item.getName())) {
        needToDeposit = true;
        break;
      }
    }

    if (!needToDeposit) {
      return true; // Nothing to deposit
    }

    // Use the built-in depositAllExcept method for faster operation
    Main.action = "Depositing all";

    if (Main.DEBUG) {
      Logger.log("Depositing all items except: " + String.join(", ", itemsToKeep));
    }

    Bank.depositAllExcept(itemsToKeep);

    // Wait for deposit to complete
    boolean success = Sleep.sleepUntil(() -> {
      for (Item item : Inventory.all()) {
        if (item != null && !keepList.contains(item.getName())) {
          return false;
        }
      }
      return true;
    }, 3000);

    if (!success && Main.DEBUG) {
      Logger.log("Failed to deposit all items");
    }

    return success;
  }

  /**
   * Walks to a named location
   * @param location The LocationData containing name and tile to walk to
   */
  public static void walkToNamedLocation(LocationData location) {
    if (!Players.getLocal().isMoving()) {
      Walking.walk(location.getTile());
      Main.action = "Walking to " + location.getName();
      if (Main.DEBUG) {
        Logger.log("Walking to " + location.getName() + " at " + location.getTile());
      }
    }
  }

  /**
   * Walks to the Grand Exchange and opens it
   *
   * @return true if GE was successfully opened, false otherwise
   */
  public static boolean openGrandExchange() {
    // Check if GE is already open
    if (GrandExchange.isOpen()) {
        return true;
    }

    // Grand Exchange location
    Tile geTile = new Tile(3165, 3487, 0);

    // Check if we need to walk to GE
    if (Players.getLocal().getTile().distance(geTile) > 10) {
        Logger.log("Walking to Grand Exchange...");

        // Walk to GE with multiple attempts
        int walkAttempts = 0;
        while (walkAttempts < 5 && Players.getLocal().getTile().distance(geTile) > 10) {
            walkAttempts++;

            if (!Players.getLocal().isMoving()) {
                Walking.walk(geTile);
                Logger.log("Walking to GE (attempt " + walkAttempts + ")");
            }

            // Wait for movement or arrival
            Sleep.sleepUntil(() ->
                Players.getLocal().getTile().distance(geTile) < 10 ||
                !Players.getLocal().isMoving(), 10000);
        }

        // If we still haven't reached GE, return false
        if (Players.getLocal().getTile().distance(geTile) > 10) {
            Logger.log("Failed to reach Grand Exchange after " + walkAttempts + " attempts");
            return false;
        }
    }

    Logger.log("Reached Grand Exchange area, looking for clerk...");

    // Find and interact with Grand Exchange clerk
    int clerkAttempts = 0;
    while (clerkAttempts < 5 && !GrandExchange.isOpen()) {
        clerkAttempts++;

        NPC clerk = NPCs.closest(npc ->
            npc != null &&
            npc.hasAction("Exchange") &&
            npc.getName() != null &&
            (npc.getName().contains("Exchange") || npc.getName().contains("clerk")));

        if (clerk != null) {
            Logger.log("Found GE clerk, attempting to interact (attempt " + clerkAttempts + ")");
            if (clerk.interact("Exchange")) {
                // Wait for GE to open
                if (Sleep.sleepUntil(GrandExchange::isOpen, 5000)) {
                    Logger.log("Successfully opened Grand Exchange");
                    return true;
                }
            }
        } else {
            Logger.log("No GE clerk found, waiting... (attempt " + clerkAttempts + ")");
        }

        Sleep.sleep(1000, 2000); // Wait before retrying
    }

    Logger.log("Failed to open Grand Exchange after " + clerkAttempts + " clerk interaction attempts");
    return false;
  }

  /**
   * Gets the current market price of an item
   *
   * @param itemName The name of the item
   * @return The market price, or -1 if not found
   */
  public static int getItemPrice(String itemName) {
    try {
        // Try to get price from GE API
        return GrandExchange.getItem(itemName).getPrice();
    } catch (Exception e) {
        Logger.log("Error getting price for " + itemName + ": " + e.getMessage());
        return -1;
    }
  }
}
