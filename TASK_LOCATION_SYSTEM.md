# Task Location System - Area and Tile Support

The task location system now supports both **Areas** and **Tiles**, giving you maximum flexibility in defining where your tasks should operate.

## Overview

The new system provides several methods in `AbstractTask`:

- `getTaskLocation()` - Returns a specific Tile (existing method)
- `getTaskArea()` - Returns an Area for the task (new method)
- `getTaskArea(int radius)` - Returns an Area with custom radius around the task tile
- `isAtTaskLocation()` - Checks if player is at the task location (supports both Area and Tile)
- `getRandomTaskTile()` - Gets a random tile within the task area

## Usage Examples

### 1. Using Only a Tile (Existing Behavior)

```java
@Override
public Tile getTaskLocation() {
    return new Tile(3273, 3180, 0); // Exact cooking spot
}
```

This will automatically create a 5x5 area around the tile when needed.

### 2. Using Only an Area

```java
private static final Area MINING_AREA = new Area(3172, 3368, 3184, 3380);

@Override
public Area getTaskArea() {
    return MINING_AREA; // Custom mining area
}
```

### 3. Using Both Area and Tile (Recommended)

```java
private static final Area FISHING_AREA = new Area(3240, 3140, 3250, 3160);

@Override
public Area getTaskArea() {
    return FISHING_AREA; // Define the working area
}

@Override
public Tile getTaskLocation() {
    return new Tile(3245, 3150, 0); // Central point for exact positioning
}
```

## New Utility Methods

### KazeUtils Methods

```java
// Walk to a task location (supports both Area and Tile)
KazeUtils.walkToTaskLocation(this);

// Walk to exact task tile (if available)
KazeUtils.walkToTaskLocationExact(this);

// Walk to any area
KazeUtils.walkToArea(someArea);
```

### AbstractTask Methods

```java
// Check if at task location (with default 5-tile radius)
if (isAtTaskLocation()) {
    // Player is at the task location
}

// Check with custom radius
if (isAtTaskLocation(10)) {
    // Player is within 10 tiles of task location
}

// Get a random tile for walking
Tile randomTile = getRandomTaskTile();
```

## Migration Guide

### Existing Tasks (No Changes Required)

Existing tasks that only use `getTaskLocation()` will continue to work exactly as before. The system automatically creates areas around tiles when needed.

### Enhanced Tasks (Recommended Updates)

For better performance and flexibility, consider updating your tasks:

**Before:**
```java
// Old way - checking specific area
if (MINE_AREA.contains(Players.getLocal().getTile())) {
    // mining logic
}

// Old way - walking to area
KazeUtils.walkToTile(MINE_AREA.getRandomTile());
```

**After:**
```java
// New way - using task location methods
if (isAtTaskLocation()) {
    // mining logic
}

// New way - using task location utilities
KazeUtils.walkToTaskLocation(this);
```

## Benefits

1. **Flexibility**: Support both precise positioning (Tiles) and area-based operations (Areas)
2. **Backward Compatibility**: All existing tasks continue to work without changes
3. **Performance**: Optimized location checking and walking utilities
4. **Consistency**: Unified API for all location-based operations
5. **Convenience**: Built-in methods for common location operations

## Real-World Examples

### Mining Task
```java
public class MyMiningTask extends AbstractTask {
    private static final Area MINE_AREA = new Area(3172, 3368, 3184, 3380);
    
    @Override
    public Area getTaskArea() {
        return MINE_AREA; // Large mining area
    }
    
    @Override
    public Tile getTaskLocation() {
        return new Tile(3178, 3374, 0); // Center of mine for exact positioning
    }
    
    @Override
    public int execute() {
        if (!isAtTaskLocation()) {
            KazeUtils.walkToTaskLocation(this); // Walks to random tile in area
            return 1000;
        }
        
        // Mining logic here
        return 600;
    }
}
```

### Cooking Task
```java
public class MyCookingTask extends AbstractTask {
    @Override
    public Tile getTaskLocation() {
        return new Tile(3273, 3180, 0); // Exact cooking range
    }
    
    @Override
    public int execute() {
        if (!isAtTaskLocation(2)) { // Within 2 tiles of cooking range
            KazeUtils.walkToTaskLocationExact(this); // Walk to exact tile
            return 1000;
        }
        
        // Cooking logic here
        return 600;
    }
}
```

## Banking Integration

The banking system automatically uses the new location system:

- After banking, it will return to your task location using `getRandomTaskTile()`
- Supports both Area and Tile-based tasks seamlessly
- Uses `isAtTaskLocation(2)` to check if return is needed

This provides a powerful, flexible system that maintains full backward compatibility while offering enhanced functionality for new tasks!
