package tasks;

import org.dreambot.api.methods.map.Area;
import org.dreambot.api.methods.map.Tile;
import org.dreambot.api.methods.skills.Skill;
import org.dreambot.api.utilities.Logger;

import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/** Base class for all tasks */
public abstract class AbstractTask {
  /**
   * Flag to indicate if this task supports banking
   */
  private boolean supportsBanking = false;

  /**
   * Time when this task started executing (in milliseconds)
   */
  private long startTime = 0;

  /**
   * Duration for this task in minutes (randomly set between 20-90 minutes)
   */
  private int durationMinutes = 0;

  /**
   * Random number generator
   */
  private static final Random random = new Random();

  /**
   * Required inventory items for this task
   * Map of item name to quantity required
   */
  private final Map<String, Integer> requiredInventoryItems = new HashMap<>();

  /**
   * Required equipment items for this task
   * List of item names that should be equipped
   */
  private final List<String> requiredEquipmentItems = new ArrayList<>();

  /**
   * Time when this task was marked as failed
   * Used to prevent selecting the task again for a while
   */
  private long failedTime = 0;

  /**
   * Time in milliseconds after which a failed task can be selected again
   */
  private static final long FAILED_TASK_TIMEOUT = 300000; // 5 minutes

  /**
   * Get the name of this task
   *
   * @return Task name
   */
  public abstract String getName();

  /**
   * Get the skill this task is for
   *
   * @return Skill enum
   */
  public abstract Skill getSkill();

  /**
   * Get the requirements for this task
   *
   * @return Requirements as a string
   */
  public abstract String getRequirements();

  /**
   * Get the location for this task
   *
   * @return Location as a string
   */
  public abstract String getLocation();

  /**
   * Get the location picture for this task
   * This image will be shown when hovering over the location in the GUI
   *
   * This can be either:
   * - A URL to an image on the web
   * - A path to a local image file (relative to the project root)
   *
   * @return The location picture URL or file path as a String, or null if not available
   */
  public String getLocationImage() {
    return null;
  }

  /**
   * Get the task location as a Tile
   * Override this method to provide a specific location for the task
   *
   * @return The task location as a Tile, or null if not specified
   */
  public Tile getTaskLocation() {
    return null;
  }

  /**
   * Get a description of what this task does
   *
   * @return Description as a string
   */
  public abstract String getDescription();

  /**
   * Check if this task can be executed
   *
   * @return true if the task can be executed
   */
  public abstract boolean accept();

  /**
   * Execute this task
   *
   * @return Sleep time in milliseconds
   */
  public abstract int execute();

  /**
   * Check if this task supports banking
   *
   * @return true if the task supports banking
   */
  public boolean supportsBanking() {
    return supportsBanking;
  }

  /**
   * Set whether this task supports banking
   *
   * @param supportsBanking true if the task supports banking
   */
  public void setSupportsBanking(boolean supportsBanking) {
    this.supportsBanking = supportsBanking;
  }

  /**
   * Start the task timer with a random duration between 20-90 minutes
   */
  public void startTimer() {
    this.startTime = System.currentTimeMillis();
    this.durationMinutes = random.nextInt(71) + 20; // Random between 20-90 minutes
    Logger.log("Started task '" + getName() + "' with duration: " + durationMinutes + " minutes");
  }

  /**
   * Check if the task has reached its random duration
   *
   * @return true if the task has been running for its set duration
   */
  public boolean hasReachedDuration() {
    if (startTime == 0 || durationMinutes <= 0) {
      return false; // Timer not started or no duration set
    }

    long durationMillis = durationMinutes * 60 * 1000L;
    long elapsed = System.currentTimeMillis() - startTime;
    boolean reached = elapsed >= durationMillis;

    if (reached) {
      Logger.log("Task '" + getName() + "' reached its duration of " + durationMinutes + " minutes");
    }

    return reached;
  }

  /**
   * Reset the task timer
   */
  public void resetTimer() {
    this.startTime = 0;
    this.durationMinutes = 0;
  }

  /**
   * Get the elapsed time in milliseconds
   *
   * @return Elapsed time in milliseconds
   */
  public long getElapsedTime() {
    if (startTime == 0) {
      return 0;
    }
    return System.currentTimeMillis() - startTime;
  }

  /**
   * Get the remaining time in minutes
   *
   * @return Remaining time in minutes, or 0 if timer not started
   */
  public int getRemainingMinutes() {
    if (startTime == 0 || durationMinutes <= 0) {
      return 0;
    }

    long durationMillis = durationMinutes * 60 * 1000L;
    long elapsed = System.currentTimeMillis() - startTime;
    long remaining = durationMillis - elapsed;

    return Math.max(0, (int)(remaining / (60 * 1000L)));
  }

  /**
   * Get the remaining time in seconds
   *
   * @return Remaining time in seconds, or 0 if timer not started
   */
  public int getRemainingSeconds() {
    if (startTime == 0 || durationMinutes <= 0) {
      return 0;
    }

    long durationMillis = durationMinutes * 60 * 1000L;
    long elapsed = System.currentTimeMillis() - startTime;
    long remaining = durationMillis - elapsed;

    return Math.max(0, (int)(remaining / 1000));
  }

  /**
   * Get the duration set for this task
   *
   * @return Duration in minutes
   */
  /**
   * Add a required inventory item for this task
   *
   * @param itemName The name of the item
   * @param quantity The quantity required
   */
  protected void addRequiredInventoryItem(String itemName, int quantity) {
    requiredInventoryItems.put(itemName, quantity);
  }

  /**
   * Add a required equipment item for this task
   *
   * @param itemName The name of the item that should be equipped
   */
  protected void addRequiredEquipmentItem(String itemName) {
    requiredEquipmentItems.add(itemName);
  }

  /**
   * Get all required inventory items for this task
   *
   * @return Map of item names to quantities
   */
  public Map<String, Integer> getRequiredInventoryItems() {
    return requiredInventoryItems;
  }

  /**
   * Get all required equipment items for this task
   *
   * @return List of item names that should be equipped
   */
  public List<String> getRequiredEquipmentItems() {
    return requiredEquipmentItems;
  }

  /**
   * Check if this task has any required items (inventory or equipment)
   *
   * @return true if the task has any required items
   */
  public boolean hasRequiredItems() {
    return !requiredInventoryItems.isEmpty() || !requiredEquipmentItems.isEmpty();
  }

  /**
   * Mark this task as failed
   * This will prevent the task from being selected again for a while
   */
  public void markAsFailed() {
    failedTime = System.currentTimeMillis();
    Logger.log("Marked task '" + getName() + "' as failed. Will not be selected for " +
              (FAILED_TASK_TIMEOUT / 60000) + " minutes.");
  }

  /**
   * Check if this task was recently marked as failed
   *
   * @return true if the task was marked as failed recently
   */
  public boolean isFailedRecently() {
    if (failedTime == 0) {
      return false;
    }

    return System.currentTimeMillis() - failedTime < FAILED_TASK_TIMEOUT;
  }

  /**
   * Get the task start time in milliseconds
   *
   * @return the start time in milliseconds
   */
  public long getStartTime() {
    return startTime;
  }

  /**
   * Get the task duration in minutes
   *
   * @return the duration in minutes
   */
  public int getDurationMinutes() {
    return durationMinutes;
  }

  /**
   * Mark this task as completed for this session
   * This is used when manually skipping a task
   */
  public void markAsCompleted() {
    // Reset the timer
    resetTimer();

    // Mark as failed with a very long timeout to prevent it from being selected again this session
    failedTime = System.currentTimeMillis();

    // Reset any task-specific state
    resetState();

    Logger.log("Marked task '" + getName() + "' as completed for this session.");
  }

  /**
   * Reset any task-specific state variables
   * Override in subclasses to add specific reset logic
   */
  protected void resetState() {
    // Base implementation does nothing
    // Override in subclasses to reset task-specific state
  }
}
