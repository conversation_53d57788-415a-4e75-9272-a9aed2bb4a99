package gui;

import Main.Main;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import gui.components.*;
import gui.theme.ModernTheme;
import java.awt.*;
import java.awt.event.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.List;
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.plaf.basic.BasicSpinnerUI;
import javax.swing.Timer;
import net.miginfocom.swing.MigLayout;
import org.dreambot.api.Client;
import org.dreambot.api.methods.skills.Skill;
import org.dreambot.api.utilities.Logger;
import tasks.AbstractTask;
import tasks.TaskManager;

public class GUI extends JFrame {
    // Colors
    private static final Color PANEL_COLOR = ModernTheme.BACKGROUND_DARK;
    private static final Color BORDER_COLOR = ModernTheme.BORDER;
    private static final Color ACCENT_COLOR = ModernTheme.ACCENT;
    private static final Color SELECTED_COLOR = new Color(82, 166, 82);  // Bright green for selected items
    private static final Color SELECTED_TEXT_COLOR = Color.WHITE;  // White text for selected items
    private static final Color SELECTED_BG_COLOR = new Color(40, 40, 45);  // Dark background for selected items
    private static final Color HOVER_COLOR = new Color(60, 60, 70);  // Color for hover effect
    private static final Color HIGHLIGHT_COLOR = new Color(103, 103, 120);  // Gold color for highlighted skill
    // Config constants
    private static final String CONFIG_EXTENSION = ".f2paio";
    // F2P skills only
    private static final Skill[] F2P_SKILLS = {
            Skill.ATTACK, Skill.STRENGTH, Skill.DEFENCE, Skill.HITPOINTS,
            Skill.RANGED, Skill.PRAYER, Skill.MAGIC, Skill.COOKING,
            Skill.WOODCUTTING, Skill.FISHING, Skill.FIREMAKING, Skill.CRAFTING,
            Skill.SMITHING, Skill.MINING, Skill.RUNECRAFTING
    };
    // Singleton instance
    private static GUI instance = null;
    // Skill UI components
    private final Map<Skill, JCheckBox> skillCheckboxes = new HashMap<>();
    private final Map<Skill, JSpinner> skillLevelSpinners = new HashMap<>();
    private final Map<Skill, JPanel> skillPanels = new HashMap<>();
    // UI Components
    private Point initialClick;
    private JPanel contentWrapper;
    private JPanel taskPanel;
    private JPanel taskListPanel;
    private Skill selectedSkill;
    private JCheckBox enableAntiban;
    private JCheckBox enableProgressTracking;
    private JCheckBox enableBreaks;
    private JCheckBox logoutAfterTime;
    private JSpinner breakFrequencySpinner;
    private JSpinner breakDurationSpinner;
    private JSpinner logoutTimeSpinner;
    private JComboBox<String> configSelector;
    private String currentConfigName = "default";
    private String lastLoadedConfig = "default";
    // Skill highlighting
    private Skill highlightedSkill = null;
    // Anti-ban settings
    private JCheckBox enableMouseMovement;
    private JCheckBox enableCameraMovement;
    private JCheckBox enableRandomBreaks;
    private JCheckBox enableRandomAFK;
    private JCheckBox enableExamineObjects;
    private JCheckBox enableRandomTabChecks;
    private JCheckBox enableHoverItems;
    private JCheckBox enableRandomRightClicks;
    private JSpinner mouseMovementFrequencySpinner;
    private JSpinner cameraMovementFrequencySpinner;
    private JSpinner randomBreaksFrequencySpinner;
    private JSpinner randomAFKFrequencySpinner;
    private JSpinner randomAFKDurationSpinner;
    private JSpinner examineObjectsFrequencySpinner;
    private JSpinner randomTabChecksFrequencySpinner;
    private JSpinner hoverItemsFrequencySpinner;
    private JSpinner randomRightClicksFrequencySpinner;
    // Track changes
    private boolean settingsChanged = false;

    public GUI(Main main) {
        super();
        if (instance != null) {
            instance.dispose();
            Logger.log("Closing existing GUI instance");
        }
        instance = this;

        try {
            // Initialize collections and ensure default config exists
            initializeCollections();
            ensureDefaultConfigExists();

            // Set up the GUI components
            setupGUI();

            // Load available configs into the dropdown
            loadAvailableConfigs();

            // Load the last used config or default
            loadLastConfig();

            // Set size and position
            setMinimumSize(new Dimension(800, 550));
            setPreferredSize(new Dimension(900, 550));
            setMaximumSize(new Dimension(1000, 550));
            pack();
            positionGUI();

            // Preload location images for tasks
            Logger.log("Preloading location images...");
            LocationImagePopup.preloadAllLocationImages();

            // Debug skill icons
            debugSkillIcons();

            // Update UI state based on current bot status
            updateUIForBotStatus();

            // Make the GUI visible
            setVisible(true);
            toFront();
            requestFocus();

            // Force a UI refresh to show the loaded config
            SwingUtilities.invokeLater(() -> {
                updateUIFromSettings();
                revalidate();
                repaint();
                Logger.log("GUI initialized with config: " + currentConfigName);
            });
        } catch (Exception e) {
            Logger.log("Error initializing GUI: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static GUI getInstance() {
        return instance;
    }

    private void setupGUI() {
        setTitle(Main.text);
        setUndecorated(true);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setResizable(false);

        JPanel mainPanel = createMainPanel();
        contentWrapper = new JPanel(new BorderLayout());
        contentWrapper.setBackground(ModernTheme.BACKGROUND_DARK);

        JTabbedPane tabbedPane = new ModernTabbedPane();
        tabbedPane.setFont(new Font("Segoe UI", Font.BOLD, 14));
        tabbedPane.addTab("Skills", createSkillsPanel());
        tabbedPane.addTab("Anti-Ban", createAntiBanPanel());
        tabbedPane.addTab("Settings", createSettingsPanel());

        contentWrapper.add(createConfigPanel(), BorderLayout.NORTH);
        contentWrapper.add(tabbedPane, BorderLayout.CENTER);
        contentWrapper.add(createButtonPanel(), BorderLayout.SOUTH);

        mainPanel.add(contentWrapper, BorderLayout.CENTER);
        setContentPane(mainPanel);
    }

    private JPanel createMainPanel() {
        JPanel panel = new JPanel(new BorderLayout(0, 0)) {
            @Override
            public void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, new Color(22, 22, 26), 0, h, new Color(16, 16, 18));
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);

                RadialGradientPaint rgp = new RadialGradientPaint(
                        w/2, h/2, Math.max(w, h),
                        new float[]{0.0f, 0.7f, 1.0f},
                        new Color[]{new Color(0, 0, 0, 0), new Color(0, 0, 0, 0), new Color(0, 0, 0, 50)}
                );
                g2d.setPaint(rgp);
                g2d.fillRect(0, 0, w, h);
                g2d.dispose();
            }
        };

        panel.setOpaque(false);
        panel.addMouseListener(new MouseAdapter() {
            @Override public void mousePressed(MouseEvent e) { initialClick = e.getPoint(); }
        });

        panel.addMouseMotionListener(new MouseMotionAdapter() {
            @Override public void mouseDragged(MouseEvent e) {
                Point p = getLocation();
                setLocation(p.x + e.getX() - initialClick.x, p.y + e.getY() - initialClick.y);
            }
        });

        return panel;
    }

    private JPanel createSkillsPanel() {
        JPanel panel = new JPanel(new MigLayout("fill, insets 0", "[grow]", "[grow]"));
        panel.setBackground(PANEL_COLOR);

        JPanel mainPanel = new JPanel(new MigLayout("fill, insets 0", "[350:350:350][grow]", "[grow]"));
        mainPanel.setBackground(PANEL_COLOR);

        JScrollPane skillsScrollPane = new ModernScrollPane(createSkillSelectionPanel());
        skillsScrollPane.setBorder(null);

        taskPanel = new JPanel(new BorderLayout());
        taskPanel.setBackground(PANEL_COLOR);
        taskPanel.setBorder(null);
        taskPanel.add(createTaskHeaderPanel(), BorderLayout.NORTH);

        taskListPanel = new JPanel(new BorderLayout());
        taskListPanel.setBackground(PANEL_COLOR);
        taskPanel.add(taskListPanel, BorderLayout.CENTER);
        taskPanel.setVisible(false);

        mainPanel.add(skillsScrollPane, "cell 0 0, grow, width 350!");
        mainPanel.add(taskPanel, "cell 1 0, grow");
        panel.add(mainPanel, "grow");

        return panel;
    }

    private JPanel createSkillSelectionPanel() {
        JPanel panel = new JPanel(new MigLayout("fillx, wrap 1, insets 10", "[grow]", "[]"));
        panel.setBackground(PANEL_COLOR);

        JLabel headerLabel = new ModernLabel("Select Skills & Target Levels");
        headerLabel.setFont(new Font("Segoe UI", Font.BOLD, 16));
        panel.add(headerLabel, "gapbottom 10");

        // Only add F2P skills
        for (Skill skill : F2P_SKILLS) {
            JPanel skillBox = createSkillPanel(skill);
            panel.add(skillBox, "growx");
            skillPanels.put(skill, skillBox);
        }

        return panel;
    }

    private JPanel createSkillPanel(Skill skill) {
        JPanel skillBox = new JPanel(new MigLayout("fillx, insets 5", "[30][grow][70]", "[]"));
        skillBox.setBackground(PANEL_COLOR);
        skillBox.setBorder(BorderFactory.createLineBorder(BORDER_COLOR, 1, true));

        // Get skill icon
        BufferedImage iconImage = Main.skillIcons.get(skill);
        JLabel iconLabel = new JLabel();

        // Create a timer to check for the icon periodically
        Timer iconCheckTimer = new Timer(500, null);
        iconCheckTimer.setRepeats(true);
        iconCheckTimer.addActionListener(e -> {
            // Check if the icon is now available
            BufferedImage updatedIcon = Main.skillIcons.get(skill);
            if (updatedIcon != null && iconLabel.getIcon() == null) {
                // Create high-quality scaled icon
                int iconSize = 23;
                BufferedImage resizedIcon = new BufferedImage(iconSize, iconSize, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g = resizedIcon.createGraphics();

                // Apply multiple high-quality rendering hints
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                // Draw with slight padding to avoid edge artifacts
                g.drawImage(updatedIcon, 0, 0, iconSize, iconSize, null);
                g.dispose();

                iconLabel.setIcon(new ImageIcon(resizedIcon));
                iconLabel.setPreferredSize(new Dimension(iconSize, iconSize));
                iconLabel.setText(""); // Clear any text
                Logger.log("Updated icon for " + skill.name() + " (delayed loading)");

                // Stop the timer once we've loaded the icon
                iconCheckTimer.stop();
            }
        });

        if (iconImage != null) {
            // Create high-quality scaled icon
            int iconSize = 23;
            BufferedImage resizedIcon = new BufferedImage(iconSize, iconSize, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g = resizedIcon.createGraphics();

            // Apply multiple high-quality rendering hints
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // Draw with slight padding to avoid edge artifacts
            g.drawImage(iconImage, 0, 0, iconSize, iconSize, null);
            g.dispose();

            iconLabel.setIcon(new ImageIcon(resizedIcon));
            iconLabel.setPreferredSize(new Dimension(iconSize, iconSize));
            Logger.log("Loaded icon for " + skill.name());
        } else {
            iconLabel.setText(skill.name().substring(0, 1));
            Logger.log("No icon found for " + skill.name() + ", using text fallback");

            // Start the timer to check for the icon periodically
            iconCheckTimer.start();
        }

        // Add the icon to the panel with fixed size
        skillBox.add(iconLabel, "cell 0 0, width 24!, height 24!");

        JLabel skillLabel = new JLabel(formatSkillName(skill.name()));
        skillLabel.setForeground(Color.WHITE);
        skillLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));

        JCheckBox skillCheckbox = new JCheckBox();
        skillCheckbox.setVisible(false);

    JSpinner levelSpinner = new JSpinner(new SpinnerNumberModel(1, 1, 99, 1));
        levelSpinner.setEnabled(false);
        JComponent editor = levelSpinner.getEditor();
        JFormattedTextField textField = ((JSpinner.DefaultEditor) editor).getTextField();
        styleSpinner(levelSpinner);
        levelSpinner.setUI(new BasicSpinnerUI() {
            @Override protected Component createNextButton() { return null; }
            @Override protected Component createPreviousButton() { return null; }
        });

        MouseAdapter mouseAdapter = new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (SwingUtilities.isRightMouseButton(e)) {
                    // Right-click to toggle this individual skill
                    toggleSkillSelection(skillCheckbox, levelSpinner, skillBox, iconLabel, textField);
                } else if (SwingUtilities.isLeftMouseButton(e)) {
                    // Left-click to highlight and show tasks
                    highlightSkill(skill, skillBox);
                    showTasksForSkill(skill);
                }
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                // Add hover effect
                if (highlightedSkill != skill) {
                    // Only change color if this isn't the highlighted skill
                    skillBox.setBackground(HOVER_COLOR);
                }
            }

            @Override
            public void mouseExited(MouseEvent e) {
                // Remove hover effect
                if (highlightedSkill != skill) {
                    // Only restore color if this isn't the highlighted skill
                    skillBox.setBackground(PANEL_COLOR);
                }
            }
        };

        skillBox.addMouseListener(mouseAdapter);
        iconLabel.addMouseListener(mouseAdapter);
        skillLabel.addMouseListener(mouseAdapter);
        levelSpinner.addMouseListener(mouseAdapter);
        textField.addMouseListener(mouseAdapter);

        skillCheckbox.addActionListener(e -> {
            levelSpinner.setEnabled(skillCheckbox.isSelected());
            settingsChanged = true;
        });

        levelSpinner.addChangeListener(e -> settingsChanged = true);
        skillBox.add(iconLabel, "cell 0 0");
        skillBox.add(skillLabel, "cell 1 0");
        skillBox.add(levelSpinner, "cell 2 0");

        skillCheckboxes.put(skill, skillCheckbox);
        skillLevelSpinners.put(skill, levelSpinner);

        return skillBox;
    }

    private JPanel createTaskHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout()) {
            @Override protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setPaint(new GradientPaint(0, 0, new Color(40, 40, 48), 0, getHeight(), new Color(35, 35, 40)));
                g2d.fillRect(0, 0, getWidth(), getHeight());
                g2d.setColor(new Color(60, 60, 70));
                g2d.drawLine(0, getHeight()-1, getWidth(), getHeight()-1);
                g2d.dispose();
            }
        };

        headerPanel.setPreferredSize(new Dimension(0, 40));
        JLabel headerLabel = new JLabel("Available Tasks");
        headerLabel.setForeground(Color.WHITE);
        headerLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
        headerLabel.setBorder(new EmptyBorder(0, 15, 0, 0));
        headerPanel.add(headerLabel, BorderLayout.CENTER);

        return headerPanel;
    }

    private JPanel createSettingsPanel() {
        try {
            // Create a container panel that will hold the scroll pane
            JPanel containerPanel = new JPanel(new BorderLayout());
            containerPanel.setBackground(PANEL_COLOR);

            // Create the content panel that will hold all the settings
            JPanel contentPanel = new JPanel(new MigLayout("fillx, wrap 1, insets 15", "[grow]", "[]"));
            contentPanel.setBackground(PANEL_COLOR);

            // General settings
            ModernLabel generalLabel = new ModernLabel("Script Behavior");
            generalLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            enableAntiban = new ModernCheckBox("Enable Anti-Ban");
            enableProgressTracking = new ModernCheckBox("Enable Progress Tracking");

            // Break settings
            ModernLabel breakLabel = new ModernLabel("Break Settings");
            breakLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            enableBreaks = new ModernCheckBox("Enable Breaks");

            JPanel breakFrequencyPanel = createSpinnerPanel("Break Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(60, 15, 240, 5)));
            JPanel breakDurationPanel = createSpinnerPanel("Break Duration (minutes):",
                    new JSpinner(new SpinnerNumberModel(5, 1, 60, 1)));

            // Logout settings
            ModernLabel logoutLabel = new ModernLabel("Logout Settings");
            logoutLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            logoutAfterTime = new ModernCheckBox("Logout After Time");
            JPanel logoutTimePanel = createSpinnerPanel("Logout After (minutes):",
                    new JSpinner(new SpinnerNumberModel(120, 30, 480, 15)));

            // Task optimization
            ModernLabel optimizationLabel = new ModernLabel("Task Optimization");
            optimizationLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            JCheckBox taskOptimizationCheckbox = new ModernCheckBox("Optimize Task Selection");
            taskOptimizationCheckbox.setSelected(Main.useTaskOptimization);
            taskOptimizationCheckbox.addActionListener(e -> {
                Main.useTaskOptimization = taskOptimizationCheckbox.isSelected();
                settingsChanged = true;
            });

            // Initialize spinners first
            breakFrequencySpinner = (JSpinner) breakFrequencyPanel.getComponent(1);
            breakDurationSpinner = (JSpinner) breakDurationPanel.getComponent(1);
            logoutTimeSpinner = (JSpinner) logoutTimePanel.getComponent(1);

            // Add components to the content panel
            contentPanel.add(generalLabel, "gaptop 10");
            contentPanel.add(enableAntiban, "gapleft 15");
            contentPanel.add(enableProgressTracking, "gapleft 15");

            contentPanel.add(breakLabel, "gaptop 15");
            contentPanel.add(enableBreaks, "gapleft 15");
            contentPanel.add(breakFrequencyPanel, "gapleft 30, growx");
            contentPanel.add(breakDurationPanel, "gapleft 30, growx");

            contentPanel.add(logoutLabel, "gaptop 15");
            contentPanel.add(logoutAfterTime, "gapleft 15");
            contentPanel.add(logoutTimePanel, "gapleft 30, growx");

            contentPanel.add(optimizationLabel, "gaptop 15");
            contentPanel.add(taskOptimizationCheckbox, "gapleft 15");

            // Add some extra space at the bottom for better scrolling
            contentPanel.add(new JLabel(" "), "gaptop 20");

            // Add listeners
            enableBreaks.addActionListener(e -> {
                boolean enabled = enableBreaks.isSelected();
                breakFrequencySpinner.setEnabled(enabled);
                breakDurationSpinner.setEnabled(enabled);
                settingsChanged = true;
            });

            enableAntiban.addActionListener(e -> settingsChanged = true);
            enableProgressTracking.addActionListener(e -> settingsChanged = true);
            logoutAfterTime.addActionListener(e -> {
                logoutTimeSpinner.setEnabled(logoutAfterTime.isSelected());
                settingsChanged = true;
            });

            // Add change listeners to spinners
            breakFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            breakDurationSpinner.addChangeListener(e -> settingsChanged = true);
            logoutTimeSpinner.addChangeListener(e -> settingsChanged = true);

            breakFrequencySpinner.setEnabled(false);
            breakDurationSpinner.setEnabled(false);
            logoutTimeSpinner.setEnabled(false);

            // Create a scroll pane for the content panel
            JScrollPane scrollPane = new ModernScrollPane(contentPanel);
            scrollPane.setBorder(null);
            scrollPane.getVerticalScrollBar().setUnitIncrement(16); // Faster scrolling

            // Add the scroll pane to the container panel
            containerPanel.add(scrollPane, BorderLayout.CENTER);

            return containerPanel;
        } catch (Exception e) {
            Logger.log("Error creating settings panel: " + e.getMessage());
            e.printStackTrace();
            // Return a simple panel if there's an error
            JPanel fallbackPanel = new JPanel(new BorderLayout());
            fallbackPanel.setBackground(PANEL_COLOR);
            JLabel errorLabel = new JLabel("Error loading settings panel: " + e.getMessage());
            errorLabel.setForeground(Color.RED);
            errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
            fallbackPanel.add(errorLabel, BorderLayout.CENTER);
            return fallbackPanel;
        }
    }

    private JPanel createAntiBanPanel() {
        try {
            // Create a container panel that will hold the scroll pane
            JPanel containerPanel = new JPanel(new BorderLayout());
            containerPanel.setBackground(PANEL_COLOR);

            // Create the content panel that will hold all the anti-ban settings
            JPanel contentPanel = new JPanel(new MigLayout("fillx, wrap 1, insets 15", "[grow]", "[]"));
            contentPanel.setBackground(PANEL_COLOR);

            // Add header
            ModernLabel headerLabel = new ModernLabel("Anti-Ban Settings");
            headerLabel.setFont(new Font("Segoe UI", Font.BOLD, 16));
            contentPanel.add(headerLabel, "gapbottom 10");

            // Mouse Movement section
            ModernLabel mouseMovementLabel = new ModernLabel("Mouse Movement");
            mouseMovementLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(mouseMovementLabel, "gaptop 15");

            enableMouseMovement = new ModernCheckBox("Enable Random Mouse Movement");
            enableMouseMovement.setSelected(true);
            contentPanel.add(enableMouseMovement, "gapleft 15");

            JPanel mouseMovementFrequencyPanel = createSpinnerPanel("Frequency (seconds):",
                    new JSpinner(new SpinnerNumberModel(30, 5, 300, 5)));
            contentPanel.add(mouseMovementFrequencyPanel, "gapleft 30, growx");
            mouseMovementFrequencySpinner = (JSpinner) mouseMovementFrequencyPanel.getComponent(1);

            // Camera Movement section
            ModernLabel cameraMovementLabel = new ModernLabel("Camera Movement");
            cameraMovementLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(cameraMovementLabel, "gaptop 15");

            enableCameraMovement = new ModernCheckBox("Enable Random Camera Movement");
            enableCameraMovement.setSelected(true);
            contentPanel.add(enableCameraMovement, "gapleft 15");

            JPanel cameraMovementFrequencyPanel = createSpinnerPanel("Frequency (seconds):",
                    new JSpinner(new SpinnerNumberModel(45, 10, 300, 5)));
            contentPanel.add(cameraMovementFrequencyPanel, "gapleft 30, growx");
            cameraMovementFrequencySpinner = (JSpinner) cameraMovementFrequencyPanel.getComponent(1);

            // Random Breaks section
            ModernLabel randomBreaksLabel = new ModernLabel("Random Micro-Breaks");
            randomBreaksLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(randomBreaksLabel, "gaptop 15");

            enableRandomBreaks = new ModernCheckBox("Enable Random Micro-Breaks");
            enableRandomBreaks.setSelected(true);
            contentPanel.add(enableRandomBreaks, "gapleft 15");

            JPanel randomBreaksFrequencyPanel = createSpinnerPanel("Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(10, 2, 60, 1)));
            contentPanel.add(randomBreaksFrequencyPanel, "gapleft 30, growx");
            randomBreaksFrequencySpinner = (JSpinner) randomBreaksFrequencyPanel.getComponent(1);

            // Random AFK section
            ModernLabel randomAFKLabel = new ModernLabel("Random AFK");
            randomAFKLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(randomAFKLabel, "gaptop 15");

            enableRandomAFK = new ModernCheckBox("Enable Random AFK");
            enableRandomAFK.setSelected(true);
            contentPanel.add(enableRandomAFK, "gapleft 15");

            JPanel randomAFKFrequencyPanel = createSpinnerPanel("Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(15, 5, 120, 5)));
            contentPanel.add(randomAFKFrequencyPanel, "gapleft 30, growx");
            randomAFKFrequencySpinner = (JSpinner) randomAFKFrequencyPanel.getComponent(1);

            JPanel randomAFKDurationPanel = createSpinnerPanel("Duration (seconds):",
                    new JSpinner(new SpinnerNumberModel(10, 3, 60, 1)));
            contentPanel.add(randomAFKDurationPanel, "gapleft 30, growx");
            randomAFKDurationSpinner = (JSpinner) randomAFKDurationPanel.getComponent(1);

            // Examine Objects section
            ModernLabel examineObjectsLabel = new ModernLabel("Examine Objects");
            examineObjectsLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(examineObjectsLabel, "gaptop 15");

            enableExamineObjects = new ModernCheckBox("Enable Random Object Examination");
            enableExamineObjects.setSelected(true);
            contentPanel.add(enableExamineObjects, "gapleft 15");

            JPanel examineObjectsFrequencyPanel = createSpinnerPanel("Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(8, 2, 60, 1)));
            contentPanel.add(examineObjectsFrequencyPanel, "gapleft 30, growx");
            examineObjectsFrequencySpinner = (JSpinner) examineObjectsFrequencyPanel.getComponent(1);

            // Random Tab Checks section
            ModernLabel randomTabChecksLabel = new ModernLabel("Random Tab Checks");
            randomTabChecksLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(randomTabChecksLabel, "gaptop 15");

            enableRandomTabChecks = new ModernCheckBox("Enable Random Tab Checks");
            enableRandomTabChecks.setSelected(true);
            contentPanel.add(enableRandomTabChecks, "gapleft 15");

            JPanel randomTabChecksFrequencyPanel = createSpinnerPanel("Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(5, 1, 30, 1)));
            contentPanel.add(randomTabChecksFrequencyPanel, "gapleft 30, growx");
            randomTabChecksFrequencySpinner = (JSpinner) randomTabChecksFrequencyPanel.getComponent(1);

            // Hover Items section
            ModernLabel hoverItemsLabel = new ModernLabel("Hover Items");
            hoverItemsLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(hoverItemsLabel, "gaptop 15");

            enableHoverItems = new ModernCheckBox("Enable Random Item Hovering");
            enableHoverItems.setSelected(true);
            contentPanel.add(enableHoverItems, "gapleft 15");

            JPanel hoverItemsFrequencyPanel = createSpinnerPanel("Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(3, 1, 20, 1)));
            contentPanel.add(hoverItemsFrequencyPanel, "gapleft 30, growx");
            hoverItemsFrequencySpinner = (JSpinner) hoverItemsFrequencyPanel.getComponent(1);

            // Random Right Clicks section
            ModernLabel randomRightClicksLabel = new ModernLabel("Random Right Clicks");
            randomRightClicksLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
            contentPanel.add(randomRightClicksLabel, "gaptop 15");

            enableRandomRightClicks = new ModernCheckBox("Enable Random Right Clicks");
            enableRandomRightClicks.setSelected(true);
            contentPanel.add(enableRandomRightClicks, "gapleft 15");

            JPanel randomRightClicksFrequencyPanel = createSpinnerPanel("Frequency (minutes):",
                    new JSpinner(new SpinnerNumberModel(4, 1, 30, 1)));
            contentPanel.add(randomRightClicksFrequencyPanel, "gapleft 30, growx");
            randomRightClicksFrequencySpinner = (JSpinner) randomRightClicksFrequencyPanel.getComponent(1);

            // Add some extra space at the bottom for better scrolling
            contentPanel.add(new JLabel(" "), "gaptop 20");

            // Add listeners for all checkboxes to enable/disable their spinners
            enableMouseMovement.addActionListener(e -> {
                mouseMovementFrequencySpinner.setEnabled(enableMouseMovement.isSelected());
                settingsChanged = true;
            });

            enableCameraMovement.addActionListener(e -> {
                cameraMovementFrequencySpinner.setEnabled(enableCameraMovement.isSelected());
                settingsChanged = true;
            });

            enableRandomBreaks.addActionListener(e -> {
                randomBreaksFrequencySpinner.setEnabled(enableRandomBreaks.isSelected());
                settingsChanged = true;
            });

            enableRandomAFK.addActionListener(e -> {
                randomAFKFrequencySpinner.setEnabled(enableRandomAFK.isSelected());
                randomAFKDurationSpinner.setEnabled(enableRandomAFK.isSelected());
                settingsChanged = true;
            });

            enableExamineObjects.addActionListener(e -> {
                examineObjectsFrequencySpinner.setEnabled(enableExamineObjects.isSelected());
                settingsChanged = true;
            });

            enableRandomTabChecks.addActionListener(e -> {
                randomTabChecksFrequencySpinner.setEnabled(enableRandomTabChecks.isSelected());
                settingsChanged = true;
            });

            enableHoverItems.addActionListener(e -> {
                hoverItemsFrequencySpinner.setEnabled(enableHoverItems.isSelected());
                settingsChanged = true;
            });

            enableRandomRightClicks.addActionListener(e -> {
                randomRightClicksFrequencySpinner.setEnabled(enableRandomRightClicks.isSelected());
                settingsChanged = true;
            });

            // Add change listeners to all spinners
            mouseMovementFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            cameraMovementFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            randomBreaksFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            randomAFKFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            randomAFKDurationSpinner.addChangeListener(e -> settingsChanged = true);
            examineObjectsFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            randomTabChecksFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            hoverItemsFrequencySpinner.addChangeListener(e -> settingsChanged = true);
            randomRightClicksFrequencySpinner.addChangeListener(e -> settingsChanged = true);

            // Create a scroll pane for the content panel
            JScrollPane scrollPane = new ModernScrollPane(contentPanel);
            scrollPane.setBorder(null);
            scrollPane.getVerticalScrollBar().setUnitIncrement(16); // Faster scrolling

            // Add the scroll pane to the container panel
            containerPanel.add(scrollPane, BorderLayout.CENTER);

            return containerPanel;
        } catch (Exception e) {
            Logger.log("Error creating anti-ban panel: " + e.getMessage());
            e.printStackTrace();
            // Return a simple panel if there's an error
            JPanel fallbackPanel = new JPanel(new BorderLayout());
            fallbackPanel.setBackground(PANEL_COLOR);
            JLabel errorLabel = new JLabel("Error loading anti-ban panel: " + e.getMessage());
            errorLabel.setForeground(Color.RED);
            errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
            fallbackPanel.add(errorLabel, BorderLayout.CENTER);
            return fallbackPanel;
        }
    }

    private JPanel createSpinnerPanel(String labelText, JSpinner spinner) {
        JPanel panel = new JPanel(new MigLayout("insets 0", "[][grow]", "[]"));
        panel.setBackground(PANEL_COLOR);
        panel.add(new ModernLabel(labelText), "cell 0 0");
        panel.add(spinner, "cell 1 0, growx");
        styleSpinner(spinner);
        return panel;
    }

    private void styleSpinner(JSpinner spinner) {
        JComponent editor = spinner.getEditor();
        if (editor instanceof JSpinner.DefaultEditor) {
            JFormattedTextField textField = ((JSpinner.DefaultEditor) editor).getTextField();
            textField.setBackground(ModernTheme.BACKGROUND_DARK);
            textField.setForeground(ModernTheme.TEXT_PRIMARY);
            textField.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(BORDER_COLOR, 1),
                    BorderFactory.createEmptyBorder(2, 5, 2, 5)
            ));
            textField.setCaretColor(ModernTheme.TEXT_PRIMARY);
            spinner.setBackground(ModernTheme.BACKGROUND_DARK);
            editor.setBackground(ModernTheme.BACKGROUND_DARK);
        }
    }

    private JPanel createConfigPanel() {
        try {
            JPanel panel = new JPanel(new MigLayout("fillx, insets 10", "[grow][100][100]", "[]"));
            panel.setBackground(PANEL_COLOR);
            panel.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, BORDER_COLOR));

            configSelector = new ModernComboBox<>();
            configSelector.setBackground(ModernTheme.BACKGROUND_DARK);
            configSelector.setForeground(ModernTheme.TEXT_PRIMARY);
            configSelector.setFocusable(false);

            JButton loadButton = new ModernButton("Load");
            loadButton.addActionListener(e -> {
                // Check if there are actual changes that need to be saved
                if (settingsChanged) {
                    Logger.log("Settings changed, showing confirmation dialog");
                    int result = JOptionPane.showConfirmDialog(this,
                            "You have unsaved changes. Load new configuration anyway?",
                            "Unsaved Changes",
                            JOptionPane.YES_NO_OPTION);

                    if (result == JOptionPane.YES_OPTION) {
                        // Reset the flag before loading
                        settingsChanged = false;
                        loadSelectedConfig();
                    }
                } else {
                    // No changes, just load
                    loadSelectedConfig();
                }
            });

            JButton saveButton = new ModernButton("Save As");
            saveButton.addActionListener(e -> {
                String newName = JOptionPane.showInputDialog(this,
                        "Enter configuration name:",
                        currentConfigName);

                if (newName != null && !newName.trim().isEmpty()) {
                    // Store the new name
                    String savedName = newName.trim();
                    currentConfigName = savedName;

                    // Save the settings
                    saveSettings();

                    // Refresh the dropdown and select the new config
                    loadAvailableConfigs();
                    if (configSelector != null) {
                        configSelector.setSelectedItem(savedName);
                    }

                    // Remember this config for next time
                    saveLastConfig();

                    // Mark as not changed
                    settingsChanged = false;

                    // Show confirmation message
                    JOptionPane.showMessageDialog(this,
                            "Settings saved as '" + savedName + "'!",
                            "Saved",
                            JOptionPane.INFORMATION_MESSAGE);

                    Logger.log("Saved settings as: " + savedName);
                }
            });

            panel.add(new ModernLabel("Configuration:"), "cell 0 0");
            panel.add(configSelector, "cell 0 0, growx, gapleft 10");
            panel.add(loadButton, "cell 1 0");
            panel.add(saveButton, "cell 1 0");

            configSelector.addActionListener(e -> {
                // Just update the selection, don't load yet
                if (configSelector.getSelectedItem() != null) {
                    currentConfigName = configSelector.getSelectedItem().toString();
                }
            });

            return panel;
        } catch (Exception e) {
            Logger.log("Error creating config panel: " + e.getMessage());
            e.printStackTrace();
            // Return a simple panel if there's an error
            JPanel fallbackPanel = new JPanel();
            fallbackPanel.add(new JLabel("Error loading config panel"));
            return fallbackPanel;
        }
    }

    private JPanel createButtonPanel() {
        try {
            JPanel panel = new JPanel(new MigLayout("fillx, insets 10", "[grow][grow]", "[]"));
            panel.setBackground(PANEL_COLOR);
            panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, BORDER_COLOR));

            JButton resetButton = new ModernButton("Reset");
            resetButton.setBackground(new Color(60, 60, 70));
            resetButton.setForeground(Color.WHITE);
            resetButton.addActionListener(e -> {
                if (settingsChanged) {
                    int result = JOptionPane.showConfirmDialog(this,
                            "You have unsaved changes. Reset anyway?",
                            "Unsaved Changes",
                            JOptionPane.YES_NO_OPTION);

                    if (result == JOptionPane.YES_OPTION) {
                        resetSettings();
                        settingsChanged = false;
                    }
                } else {
                    resetSettings();
                }
            });

            JButton startButton = new ModernButton("Start Script");
            startButton.setBackground(ACCENT_COLOR);
            startButton.setForeground(Color.WHITE);
            startButton.addActionListener(e -> {
                // Update Main settings from UI before starting
                updateMainFromUI();

                // Set the running flag to true
                Main.running = true;
                Logger.log("Script set to running!");

                // Close the GUI and start the script
                dispose();
                Logger.log("Starting script with " + Main.targetSkills.size() + " selected skills");
            });

            panel.add(resetButton, "cell 0 0, growx");
            panel.add(startButton, "cell 1 0, growx");

            return panel;
        } catch (Exception e) {
            Logger.log("Error creating button panel: " + e.getMessage());
            e.printStackTrace();
            // Return a simple panel if there's an error
            JPanel fallbackPanel = new JPanel();
            fallbackPanel.add(new JLabel("Error loading button panel"));
            return fallbackPanel;
        }
    }

    private void highlightSkill(Skill skill, JPanel skillPanel) {
        // If there's already a highlighted skill, unhighlight it
        if (highlightedSkill != null && highlightedSkill != skill) {
            JPanel oldPanel = skillPanels.get(highlightedSkill);
            if (oldPanel != null) {
                oldPanel.setBackground(PANEL_COLOR);
            }
        }

        // Highlight the new skill
        if (highlightedSkill != skill) {
            highlightedSkill = skill;
            skillPanel.setBackground(HIGHLIGHT_COLOR);
            Logger.log("Highlighted skill: " + skill.name());
        }
    }

    private void showTasksForSkill(Skill skill) {
        selectedSkill = skill;
        taskListPanel.removeAll();

        JPanel taskList = new JPanel(new MigLayout("fillx, wrap 1, insets 0", "[grow]", "[]"));
        taskList.setBackground(PANEL_COLOR);

        List<AbstractTask> tasks = TaskManager.getTasksForSkill(skill);
        if (tasks.isEmpty()) {
            JLabel noTasksLabel = new ModernLabel("No tasks available for this skill");
            noTasksLabel.setHorizontalAlignment(SwingConstants.CENTER);
            taskList.add(noTasksLabel, "growx, gaptop 20");
        } else {
            tasks.forEach(task -> taskList.add(createTaskPanel(task), "growx"));
        }

        JScrollPane scrollPane = new ModernScrollPane(taskList);
        scrollPane.setBorder(null);
        taskListPanel.add(scrollPane, BorderLayout.CENTER);
        taskPanel.setVisible(true);
        taskListPanel.revalidate();
        taskListPanel.repaint();
    }

    private JPanel createTaskPanel(AbstractTask task) {
        JPanel panel = new JPanel(new MigLayout("fillx, insets 10", "[grow][][]", "[][][]"));
        panel.setBackground(PANEL_COLOR);
        panel.setBorder(BorderFactory.createLineBorder(BORDER_COLOR, 1, true));

        JLabel nameLabel = new ModernLabel(task.getName());
        nameLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));

        JLabel descLabel = new ModernLabel(task.getDescription());
        descLabel.setFont(new Font("Segoe UI", Font.ITALIC, 12));
        descLabel.setForeground(new Color(200, 200, 200));

        JLabel reqLabel = new ModernLabel("Requirements: " + task.getRequirements());
        reqLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        reqLabel.setForeground(new Color(180, 180, 180));

        JLabel locLabel = new ModernLabel("Location: " + task.getLocation());
        locLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        locLabel.setForeground(new Color(180, 180, 180));

        // Add location image popup on hover
        if (task.getLocationImage() != null) {
            // Create a popup for the location image
            LocationImagePopup imagePopup = new LocationImagePopup();

            // Add mouse listeners for hover effect
            locLabel.addMouseListener(new MouseAdapter() {
                private Timer hoverTimer;

                @Override
                public void mouseEntered(MouseEvent e) {
                    // Cancel any existing timer
                    if (hoverTimer != null && hoverTimer.isRunning()) {
                        hoverTimer.stop();
                    }

                    // Create a new timer with a short delay (200ms)
                    hoverTimer = new Timer(200, evt -> {
                        // Set the image URL and show the popup
                        imagePopup.setImageUrl(task.getLocationImage());

                        // Let the popup handle positioning to ensure it stays on screen
                        imagePopup.show(locLabel, 0, locLabel.getHeight());
                    });

                    hoverTimer.setRepeats(false); // Only trigger once
                    hoverTimer.start();
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    // Cancel the timer if it's still running
                    if (hoverTimer != null && hoverTimer.isRunning()) {
                        hoverTimer.stop();
                    }

                    // Hide the popup when mouse exits
                    imagePopup.setVisible(false);
                }
            });

            // Add a hand cursor to indicate it's hoverable
            locLabel.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        }

        JCheckBox checkbox = new ModernCheckBox("");
        checkbox.setVisible(false);
        checkbox.setBackground(PANEL_COLOR);

        boolean isSelected = Main.selectedTasks != null && Main.selectedTasks.stream()
                .anyMatch(t -> (t instanceof AbstractTask && ((AbstractTask) t).getName().equals(task.getName())) ||
                        (t instanceof String && t.equals(task.getName())));

        checkbox.setSelected(isSelected);
        if (isSelected) panel.setBorder(BorderFactory.createLineBorder(SELECTED_COLOR, 1, true));

        // Add bank button if task supports banking
        JPanel controlPanel = new JPanel(new MigLayout("insets 0", "[]", "[]"));
        controlPanel.setBackground(PANEL_COLOR);

        if (task.supportsBanking()) {
            JCheckBox bankCheckbox = new ModernCheckBox("Bank");
            bankCheckbox.setBackground(PANEL_COLOR);
            bankCheckbox.setSelected(Main.tasksThatBank.contains(task.getName()));
            bankCheckbox.addActionListener(e -> {
                if (bankCheckbox.isSelected()) {
                    Main.tasksThatBank.add(task.getName());
                } else {
                    Main.tasksThatBank.remove(task.getName());
                }
                settingsChanged = true;
            });
            controlPanel.add(bankCheckbox, "wrap");
        }

        controlPanel.add(checkbox, "center");

        MouseAdapter mouseAdapter = new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                checkbox.setSelected(!checkbox.isSelected());
                updateTaskSelection(task, checkbox.isSelected(), panel);
            }
        };

        panel.addMouseListener(mouseAdapter);
        checkbox.addActionListener(e -> updateTaskSelection(task, checkbox.isSelected(), panel));

        panel.add(nameLabel, "cell 0 0");
        panel.add(controlPanel, "cell 1 0 1 3, center");
        panel.add(descLabel, "cell 0 1, growx");
        panel.add(reqLabel, "cell 0 2, split 2");
        panel.add(locLabel, "gapleft 15");

        return panel;
    }

    private void updateTaskSelection(AbstractTask task, boolean selected, JPanel panel) {
        panel.setBorder(BorderFactory.createLineBorder(selected ? SELECTED_COLOR : BORDER_COLOR, 1, true));

        if (Main.selectedTasks == null) Main.selectedTasks = new ArrayList<>();

        if (selected) {
            Main.selectedTasks.add(task);
        } else {
            Main.selectedTasks.removeIf(t ->
                    (t instanceof AbstractTask && ((AbstractTask) t).getName().equals(task.getName())) ||
                            (t instanceof String && t.equals(task.getName()))
            );
        }

        // Mark settings as changed but don't save yet
        settingsChanged = true;
    }

    private void toggleSkillSelection(JCheckBox checkbox, JSpinner spinner, JPanel panel,
                                      JLabel iconLabel, JFormattedTextField textField) {
        boolean selected = !checkbox.isSelected();
        Logger.log("Toggling skill selection: current=" + checkbox.isSelected() + ", new=" + selected);

        checkbox.setSelected(selected);
        spinner.setEnabled(selected);
        textField.setBackground(selected ? SELECTED_BG_COLOR : PANEL_COLOR);
        textField.setForeground(selected ? SELECTED_TEXT_COLOR : Color.LIGHT_GRAY);
        panel.setBorder(BorderFactory.createLineBorder(selected ? SELECTED_COLOR : BORDER_COLOR, 2, true));

        for (Component comp : panel.getComponents()) {
            if (comp instanceof JLabel && comp != iconLabel) {
                JLabel label = (JLabel) comp;
                label.setFont(new Font("Segoe UI", selected ? Font.BOLD : Font.PLAIN, 12));
                label.setForeground(selected ? SELECTED_TEXT_COLOR : Color.LIGHT_GRAY);
                break;
            }
        }

        // Find the skill for this panel
        Skill skill = null;
        for (Map.Entry<Skill, JPanel> entry : skillPanels.entrySet()) {
            if (entry.getValue() == panel) {
                skill = entry.getKey();
                break;
            }
        }

        if (skill != null) {
            Logger.log("Toggled skill: " + skill.name() + ", selected=" + selected);

            // Store the previous state of targetSkills for this skill
            boolean wasEnabled = Main.targetSkills.containsKey(skill);

            // Update Main.targetSkills directly
            if (selected) {
                Main.targetSkills.put(skill, (int) spinner.getValue());
            } else {
                Main.targetSkills.remove(skill);
            }

            // If the skill's enabled state changed, reinitialize tasks
            if (wasEnabled != selected) {
                Logger.log("Skill " + skill.name() + " enabled state changed. Reinitializing tasks...");
                TaskManager.initializeTasks();

                // If tasks are displayed for this skill, refresh them
                if (selectedSkill == skill) {
                    showTasksForSkill(skill);
                }
            }
        } else {
            Logger.log("Could not find skill for panel");
        }

        // Mark settings as changed but don't save yet
        settingsChanged = true;

        // Force UI refresh
        panel.revalidate();
        panel.repaint();
    }

    private String formatSkillName(String name) {
        return name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();
    }

    private void positionGUI() {
        try {
            Canvas canvas = Client.getCanvas();
            if (canvas != null) {
                Point clientLocation = canvas.getLocationOnScreen();
                setLocation(
                        clientLocation.x + (canvas.getWidth() - getWidth()) / 2,
                        clientLocation.y + (canvas.getHeight() - getHeight()) / 2
                );
            } else {
                setLocationRelativeTo(null);
            }
        } catch (Exception e) {
            setLocationRelativeTo(null);
            if (Main.DEBUG) Logger.log("Error positioning GUI: " + e.getMessage());
        }
        setAlwaysOnTop(true);
    }

    /**
     * Debug method to check if skill icons are available
     */
    private void debugSkillIcons() {
        Logger.log("=== DEBUG: Skill Icons in GUI ====");
        Logger.log("Main.skillIcons: " + (Main.skillIcons != null ? "Available" : "NULL"));

        if (Main.skillIcons != null) {
            Logger.log("Number of skill icons: " + Main.skillIcons.size());

            // Check each F2P skill
            for (Skill skill : F2P_SKILLS) {
                BufferedImage icon = Main.skillIcons.get(skill);
                Logger.log(skill.name() + ": " + (icon != null ? "LOADED" : "NOT LOADED"));
            }
        }

        Logger.log("=================================");
    }

    private void initializeCollections() {
        if (Main.targetSkills == null) Main.targetSkills = new HashMap<>();
        if (Main.selectedTasks == null) Main.selectedTasks = new ArrayList<>();
    }

    public File getConfigDirectory() {
        try {
            // Get the script directory - fallback to user home if scripts.path is not set
            String scriptDir;
            String scriptsPath = System.getProperty("scripts.path");

            // Get script name - use a default if Main.text is null
            String scriptName = "F2PAio";
            if (Main.text != null && !Main.text.isEmpty()) {
                scriptName = Main.text;
            } else {
                Logger.log("Main.text is null, using default script name: " + scriptName);
            }

            if (scriptsPath != null && !scriptsPath.isEmpty()) {
                scriptDir = scriptsPath + File.separator + scriptName;
            } else {
                // Fallback to user home directory
                scriptDir = System.getProperty("user.home") + File.separator + scriptName;
                Logger.log("scripts.path not set, using fallback directory: " + scriptDir);
            }

            // Create configs directory
            File configsDir = new File(scriptDir, "configs");
            if (!configsDir.exists()) {
                boolean created = configsDir.mkdirs();
                if (created) {
                    Logger.log("Created config directory at: " + configsDir.getAbsolutePath());
                } else {
                    Logger.log("Failed to create config directory at: " + configsDir.getAbsolutePath());
                }
            }
            return configsDir;
        } catch (Exception e) {
            Logger.log("Error getting config directory: " + e.getMessage());
            e.printStackTrace();

            // Fallback to a temporary directory
            File tempDir = new File(System.getProperty("java.io.tmpdir"), "F2PAio/configs");
            tempDir.mkdirs();
            Logger.log("Using temporary directory for configs: " + tempDir.getAbsolutePath());
            return tempDir;
        }
    }

    private File getConfigFile() {
        return new File(getConfigDirectory(), currentConfigName + CONFIG_EXTENSION);
    }

    private File getLastConfigFile() {
        return new File(getConfigDirectory(), "last_config.txt");
    }

    private void loadLastConfig() {
        try {
            File lastConfigFile = getLastConfigFile();
            if (lastConfigFile.exists()) {
                try (BufferedReader reader = new BufferedReader(new FileReader(lastConfigFile))) {
                    String configName = reader.readLine();
                    if (configName != null && !configName.isEmpty()) {
                        lastLoadedConfig = configName;
                        currentConfigName = configName;
                        Logger.log("Last used config: " + configName);
                    }
                } catch (IOException e) {
                    Logger.log("Error reading last config: " + e.getMessage());
                }
            } else {
                Logger.log("No last config file found, using default");
                currentConfigName = "default";
            }

            // Load the settings from the config file
            loadSettingsFromJson();

            // Make sure the config selector shows the correct config
            if (configSelector != null) {
                configSelector.setSelectedItem(currentConfigName);
            }

            Logger.log("Loaded last config: " + currentConfigName + ", Main.targetSkills size: " + Main.targetSkills.size());
        } catch (Exception e) {
            Logger.log("Error loading last config: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void loadSelectedConfig() {
        try {
            if (configSelector == null || configSelector.getSelectedItem() == null) {
                Logger.log("Cannot load config: config selector or selected item is null");
                return;
            }

            // Reset the settings changed flag before loading
            settingsChanged = false;

            String selected = configSelector.getSelectedItem().toString();
            currentConfigName = selected;
            loadSettingsFromJson();

            // Update UI on the EDT to ensure proper rendering
            SwingUtilities.invokeLater(() -> {
                try {
                    // Reset the settings changed flag again before updating UI
                    settingsChanged = false;

                    updateUIFromSettings();

                    // Force a complete UI refresh (F2P skills only)
                    for (Skill skill : F2P_SKILLS) {
                        JPanel panel = skillPanels.get(skill);
                        if (panel != null) {
                            panel.revalidate();
                            panel.repaint();
                        }
                    }

                    contentWrapper.revalidate();
                    contentWrapper.repaint();
                    revalidate();
                    repaint();

                    // Reset the settings changed flag one more time after UI update
                    settingsChanged = false;

                    Logger.log("UI updated for config: " + selected + ", settingsChanged=" + settingsChanged);
                } catch (Exception e) {
                    Logger.log("Error updating UI: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            saveLastConfig(); // Save the newly loaded config as last used
            Logger.log("Loaded config: " + selected + ", settingsChanged=" + settingsChanged);
        } catch (Exception e) {
            Logger.log("Error loading selected config: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void loadSettingsFromJson() {
        try {
            File configFile = getConfigFile();
            if (!configFile.exists()) {
                Logger.log("No config file found for '" + currentConfigName + "', using default settings");

                // If we're trying to load a non-default config that doesn't exist, try to load the default config
                if (!currentConfigName.equals("default")) {
                    currentConfigName = "default";
                    configFile = getConfigFile();
                    if (!configFile.exists()) {
                        Logger.log("Default config file not found, creating it");
                        createDefaultConfig(configFile);
                    }
                } else {
                    // We're already trying to load the default config, but it doesn't exist
                    Logger.log("Creating default config");
                    createDefaultConfig(configFile);
                }

                // If we still don't have a config file, return
                if (!configFile.exists()) {
                    Logger.log("Failed to create or find any config file");
                    return;
                }
            }

            String json = readFileContents(configFile);
            Map<String, Object> config = new Gson().fromJson(json, new TypeToken<Map<String, Object>>(){}.getType());

            // Reset all collections
            Main.targetSkills.clear();
            if (Main.selectedTasks != null) {
                Main.selectedTasks.clear();
            } else {
                Main.selectedTasks = new ArrayList<>();
            }

            // Load target skills with levels
            if (config.containsKey("targetSkills")) {
                Logger.log("Found targetSkills in config");
                try {
                    // Get the targetSkills from the config
                    Object targetSkillsObj = config.get("targetSkills");
                    Logger.log("targetSkills type: " + (targetSkillsObj != null ? targetSkillsObj.getClass().getName() : "null"));

                    // Convert to Map<String, Double>
                    Map<String, Double> targetSkills;
                    if (targetSkillsObj instanceof Map) {
                        targetSkills = (Map<String, Double>) targetSkillsObj;
                        Logger.log("targetSkills size: " + targetSkills.size());

                        // Print all entries for debugging
                        for (Map.Entry<String, ?> entry : targetSkills.entrySet()) {
                            Logger.log("Config entry: " + entry.getKey() + " = " + entry.getValue() +
                                      " (" + (entry.getValue() != null ? entry.getValue().getClass().getName() : "null") + ")");
                        }

                        // Process each skill
                        for (Map.Entry<String, ?> entry : targetSkills.entrySet()) {
                            try {
                                String skillName = entry.getKey().toUpperCase();
                                Skill skill = Skill.valueOf(skillName);

                                // Handle different types of values
                                int level;
                                Object value = entry.getValue();
                                if (value instanceof Number) {
                                    level = ((Number) value).intValue();
                                } else if (value instanceof String) {
                                    level = Integer.parseInt((String) value);
                                } else {
                                    level = 99; // Default to 99 if unknown type
                                    Logger.log("Unknown value type for skill " + skillName + ": " +
                                              (value != null ? value.getClass().getName() : "null"));
                                }

                                // Add to Main.targetSkills
                                Main.targetSkills.put(skill, level);
                                Logger.log("Added skill to targetSkills: " + skillName + " = " + level);
                            } catch (IllegalArgumentException e) {
                                Logger.log("Unknown skill in targetSkills: " + entry.getKey());
                            } catch (Exception e) {
                                Logger.log("Error processing skill " + entry.getKey() + ": " + e.getMessage());
                            }
                        }

                        Logger.log("Main.targetSkills size after loading: " + Main.targetSkills.size());
                    } else {
                        Logger.log("targetSkills is not a Map: " +
                                  (targetSkillsObj != null ? targetSkillsObj.getClass().getName() : "null"));
                    }
                } catch (Exception e) {
                    Logger.log("Error processing targetSkills: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                Logger.log("No targetSkills found in config");
            }

            // Load other settings
            Main.antibanEnabled = getConfigBoolean(config, "antibanEnabled", true);
            Main.afkEnabled = getConfigBoolean(config, "progressTrackingEnabled", true);
            Main.breaksEnabled = getConfigBoolean(config, "breaksEnabled", true);
            Main.breakFrequency = getConfigNumber(config, "breakFrequency", 60).intValue();
            Main.breakDuration = getConfigNumber(config, "breakDuration", 5).intValue();
            Main.logoutAfterTime = getConfigBoolean(config, "logoutAfterTime", false);
            Main.logoutTime = getConfigNumber(config, "logoutTime", 120).intValue();
            Main.useTaskOptimization = getConfigBoolean(config, "taskOptimization", true);

            // Load banking preferences
            Main.tasksThatBank.clear();
            if (config.containsKey("tasksThatBank")) {
                try {
                    List<String> tasksThatBank = (List<String>) config.get("tasksThatBank");
                    if (tasksThatBank != null) {
                        Main.tasksThatBank.addAll(tasksThatBank);
                        Logger.log("Loaded banking preferences: " + tasksThatBank.size() + " tasks");
                    }
                } catch (Exception e) {
                    Logger.log("Error loading banking preferences: " + e.getMessage());
                }
            }

            // Load anti-ban settings
            if (config.containsKey("antiBanSettings")) {
                try {
                    Map<String, Object> antiBanSettings = (Map<String, Object>) config.get("antiBanSettings");
                    if (antiBanSettings != null) {
                        Main.antiBanSettings = antiBanSettings;
                        Logger.log("Loaded anti-ban settings: " + antiBanSettings.size() + " settings");
                    }
                } catch (Exception e) {
                    Logger.log("Error loading anti-ban settings: " + e.getMessage());
                }
            } else {
                // Create default anti-ban settings if not present
                Main.antiBanSettings = new HashMap<>();

                // Mouse movement
                Main.antiBanSettings.put("enableMouseMovement", true);
                Main.antiBanSettings.put("mouseMovementFrequency", 30);

                // Camera movement
                Main.antiBanSettings.put("enableCameraMovement", true);
                Main.antiBanSettings.put("cameraMovementFrequency", 45);

                // Random breaks
                Main.antiBanSettings.put("enableRandomBreaks", true);
                Main.antiBanSettings.put("randomBreaksFrequency", 10);

                // Random AFK
                Main.antiBanSettings.put("enableRandomAFK", true);
                Main.antiBanSettings.put("randomAFKFrequency", 15);
                Main.antiBanSettings.put("randomAFKDuration", 10);

                // Examine objects
                Main.antiBanSettings.put("enableExamineObjects", true);
                Main.antiBanSettings.put("examineObjectsFrequency", 8);

                // Random tab checks
                Main.antiBanSettings.put("enableRandomTabChecks", true);
                Main.antiBanSettings.put("randomTabChecksFrequency", 5);

                // Hover items
                Main.antiBanSettings.put("enableHoverItems", true);
                Main.antiBanSettings.put("hoverItemsFrequency", 3);

                // Random right clicks
                Main.antiBanSettings.put("enableRandomRightClicks", true);
                Main.antiBanSettings.put("randomRightClicksFrequency", 4);

                Logger.log("Created default anti-ban settings");
            }

            // Load selected tasks
            if (config.containsKey("selectedTasks")) {
                List<String> taskNames = (List<String>) config.get("selectedTasks");
                for (String taskName : taskNames) {
                    AbstractTask task = TaskManager.getTaskByName(taskName);
                    if (task != null) {
                        Main.selectedTasks.add(task);
                    } else {
                        Main.selectedTasks.add(taskName);
                    }
                }
            }

            // Reinitialize tasks based on the loaded skills
            Logger.log("Reinitializing tasks based on loaded configuration...");
            TaskManager.initializeTasks();

            Logger.log("Settings loaded from " + configFile.getAbsolutePath());
        } catch (Exception e) {
            Logger.log("Error loading settings: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void saveLastConfig() {
        File lastConfigFile = getLastConfigFile();
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(lastConfigFile))) {
            writer.write(currentConfigName);
            Logger.log("Saved last config: " + currentConfigName);
        } catch (IOException e) {
            Logger.log("Error saving last config: " + e.getMessage());
        }
    }

    private void loadAvailableConfigs() {
        try {
            if (configSelector == null) {
                Logger.log("Config selector is null, cannot load configs");
                return;
            }

            // Remember the current selection
            String currentSelection = currentConfigName;
            Logger.log("Current config before refresh: " + currentSelection);

            // Clear and reload items
            configSelector.removeAllItems();
            File configsDir = getConfigDirectory();

            // Add default first
            configSelector.addItem("default");

            // Add all other configs
            File[] configFiles = configsDir.listFiles((dir, name) -> name.endsWith(CONFIG_EXTENSION));
            if (configFiles != null) {
                for (File file : configFiles) {
                    String name = file.getName().replace(CONFIG_EXTENSION, "");
                    if (!name.equals("default")) {
                        configSelector.addItem(name);
                        Logger.log("Added config to dropdown: " + name);
                    }
                }
            }

            // Restore the selection
            configSelector.setSelectedItem(currentSelection);
            Logger.log("Loaded available configs, selected: " + configSelector.getSelectedItem());
        } catch (Exception e) {
            Logger.log("Error loading configurations: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void ensureDefaultConfigExists() {
        try {
            File configsDir = getConfigDirectory();
            if (configsDir == null || !configsDir.exists()) {
                Logger.log("Config directory does not exist or could not be created");
                return;
            }

            File defaultConfig = new File(configsDir, "default" + CONFIG_EXTENSION);
            if (!defaultConfig.exists()) {
                Logger.log("Default config does not exist, creating it at: " + defaultConfig.getAbsolutePath());
                createDefaultConfig(defaultConfig);

                // Verify the file was created
                if (defaultConfig.exists()) {
                    Logger.log("Successfully created default config");
                } else {
                    Logger.log("Failed to create default config file");
                }
            } else {
                Logger.log("Default config exists at " + defaultConfig.getAbsolutePath());
            }
        } catch (Exception e) {
            Logger.log("Error ensuring default config exists: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void createDefaultConfig(File configFile) {
        try {
            // Ensure parent directory exists
            File parentDir = configFile.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    Logger.log("Failed to create parent directories: " + parentDir.getAbsolutePath());
                    return;
                }
            }

            Map<String, Object> defaultConfig = new HashMap<>();

            // Add all F2P skills with level 99 by default
            List<String> enabledSkills = new ArrayList<>();
            Map<String, Integer> targetSkills = new HashMap<>();

            // Only add F2P skills
            for (Skill skill : F2P_SKILLS) {
                String skillName = skill.name();
                enabledSkills.add(skillName);
                targetSkills.put(skillName, 99); // Set default level to 99
                Logger.log("Added F2P skill to default config: " + skillName);
            }

            defaultConfig.put("enabledSkills", enabledSkills);
            defaultConfig.put("targetSkills", targetSkills);

            // Add default settings
            defaultConfig.put("antibanEnabled", true);
            defaultConfig.put("progressTrackingEnabled", true);
            defaultConfig.put("breaksEnabled", true);
            defaultConfig.put("breakFrequency", 60);
            defaultConfig.put("breakDuration", 5);
            defaultConfig.put("logoutAfterTime", false);
            defaultConfig.put("logoutTime", 120);
            defaultConfig.put("taskOptimization", true);

            // Add default anti-ban settings
            Map<String, Object> antiBanSettings = new HashMap<>();
            // ... [anti-ban settings remain unchanged] ...
            defaultConfig.put("antiBanSettings", antiBanSettings);

            // Create a list of hardcoded task names for common F2P tasks

            try {
                // Try to get tasks for each skill
                for (Skill skill : F2P_SKILLS) {
                    List<AbstractTask> tasksForSkill = TaskManager.getTasksForSkill(skill);
                    if (tasksForSkill != null && !tasksForSkill.isEmpty()) {
                        Logger.log("Found " + tasksForSkill.size() + " tasks for skill: " + skill.name());
                        for (AbstractTask task : tasksForSkill) {
                            String taskName = task.getName();
                            if (!defaultEnabledTasks.getDefaultTasks().contains(taskName)) {
                                defaultEnabledTasks.getDefaultTasks().add(taskName);
                                Logger.log("Added task from skill " + skill.name() + ": " + taskName);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                Logger.log("Error getting tasks from TaskManager: " + e.getMessage());
                e.printStackTrace();
            }

            // Set selected tasks in the config
            defaultConfig.put("selectedTasks", defaultEnabledTasks.getDefaultTasks());

            // Add default banking preferences
            defaultConfig.put("tasksThatBank", defaultEnabledTasks.getDefaultTasks());

            // Convert to JSON and write to file
            String json = new GsonBuilder().setPrettyPrinting().create().toJson(defaultConfig);
            Logger.log("Writing default config with " + defaultEnabledTasks.getDefaultTasks().size() + " tasks");

            try (FileWriter writer = new FileWriter(configFile)) {
                writer.write(json);
                writer.flush();
            }

            // Verify the file was created and contains the expected tasks
            if (configFile.exists()) {
                Logger.log("Default config created at: " + configFile.getAbsolutePath());

                // Verify the content
                try {
                    String content = readFileContents(configFile);
                    Logger.log("Config file size: " + content.length() + " bytes");

                    // Parse the JSON to verify it's valid
                    Map<String, Object> parsedConfig = new Gson().fromJson(content, new TypeToken<Map<String, Object>>(){}.getType());
                    List<String> savedTasks = (List<String>) parsedConfig.get("selectedTasks");
                    Logger.log("Verified config contains " + (savedTasks != null ? savedTasks.size() : 0) + " tasks");

                    // Log all saved tasks for debugging
                    if (savedTasks != null) {
                        for (String taskName : savedTasks) {
                            Logger.log("Saved task: " + taskName);
                        }
                    }
                } catch (Exception e) {
                    Logger.log("Error verifying config: " + e.getMessage());
                }
            } else {
                Logger.log("Failed to create config file");
            }
        } catch (Exception e) {
            Logger.log("Error creating default config: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String readFileContents(File file) throws IOException {
        if (!file.exists() || file.length() == 0) {
            Logger.log("File does not exist or is empty: " + file.getAbsolutePath());
            throw new IOException("File does not exist or is empty: " + file.getAbsolutePath());
        }

        StringBuilder builder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
        }

        String content = builder.toString();
        if (content.isEmpty()) {
            Logger.log("File is empty after reading: " + file.getAbsolutePath());
            throw new IOException("File is empty after reading: " + file.getAbsolutePath());
        }

        return content;
    }

    private void updateUIFromSettings() {
        Logger.log("Updating UI from settings");
        Logger.log("Main.targetSkills size: " + Main.targetSkills.size());

        try {
            // Store the current state of the settingsChanged flag
            final boolean wasChanged = settingsChanged;
            Logger.log("Preserving settingsChanged flag: " + wasChanged);

            // Make a copy of the skills to avoid race conditions
            final Map<Skill, Integer> skillsCopy = new HashMap<>(Main.targetSkills);
            Logger.log("Made copy of skills, size: " + skillsCopy.size());

            // Print all skills for debugging
            Logger.log("Skills to update in UI:");
            for (Map.Entry<Skill, Integer> entry : skillsCopy.entrySet()) {
                Logger.log("  " + entry.getKey().name() + " = " + entry.getValue());
            }

            // Update UI on the EDT
            SwingUtilities.invokeLater(() -> {
                try {
                    // First reset all skills to ensure proper state
                    resetAllSkillsUI();
                    Logger.log("UI reset complete");

                    // Immediately update all skills without delay
                    for (Map.Entry<Skill, Integer> entry : skillsCopy.entrySet()) {
                        Skill skill = entry.getKey();
                        int level = entry.getValue();

                        // Get UI components
                        JCheckBox checkbox = skillCheckboxes.get(skill);
                        JSpinner spinner = skillLevelSpinners.get(skill);
                        JPanel panel = skillPanels.get(skill);

                        if (checkbox != null && spinner != null && panel != null) {
                            // Force selection state
                            checkbox.setSelected(true);
                            spinner.setValue(level);
                            spinner.setEnabled(true);

                            // Update visual styling with a bright border
                            panel.setBorder(BorderFactory.createLineBorder(
                                SELECTED_COLOR, // Bright green
                                1, // Thick border
                                true
                            ));

                            // Update text field
                            JComponent editor = spinner.getEditor();
                            JFormattedTextField textField = ((JSpinner.DefaultEditor) editor).getTextField();
                            textField.setBackground(new Color(40, 40, 45));
                            textField.setForeground(Color.WHITE);

                            // Update label styling
                            for (Component comp : panel.getComponents()) {
                                if (comp instanceof JLabel) {
                                    JLabel label = (JLabel) comp;
                                    if (!(label.getIcon() instanceof ImageIcon)) {
                                        label.setFont(new Font("Segoe UI", Font.BOLD, 12));
                                        label.setForeground(Color.WHITE);
                                    }
                                }
                            }

                            // Force repaint
                            panel.revalidate();
                            panel.repaint();

                            Logger.log("Updated UI for skill: " + skill.name() + " = " + level);
                        }
                    }

                    // Force a complete UI refresh
                    contentWrapper.revalidate();
                    contentWrapper.repaint();
                    revalidate();
                    repaint();

                    // Restore the settingsChanged flag to its previous state
                    settingsChanged = wasChanged;

                    Logger.log("UI update complete, updated " + skillsCopy.size() + " skills, settingsChanged=" + settingsChanged);
                } catch (Exception e) {
                    Logger.log("Error updating UI: " + e.getMessage());
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            Logger.log("Error in updateUIFromSettings: " + e.getMessage());
            e.printStackTrace();
        }

        // Update other settings
        enableAntiban.setSelected(Main.antibanEnabled);
        enableProgressTracking.setSelected(Main.afkEnabled);

        // Update break settings
        enableBreaks.setSelected(Main.breaksEnabled);
        breakFrequencySpinner.setValue(Main.breakFrequency);
        breakDurationSpinner.setValue(Main.breakDuration);
        breakFrequencySpinner.setEnabled(Main.breaksEnabled);
        breakDurationSpinner.setEnabled(Main.breaksEnabled);

        // Update logout settings
        logoutAfterTime.setSelected(Main.logoutAfterTime);
        logoutTimeSpinner.setValue(Main.logoutTime);
        logoutTimeSpinner.setEnabled(Main.logoutAfterTime);

        // Update anti-ban settings if they exist
        if (Main.antiBanSettings != null) {
            // Mouse movement
            if (enableMouseMovement != null) {
                boolean enabled = getAntiBanBoolean("enableMouseMovement", true);
                enableMouseMovement.setSelected(enabled);
                mouseMovementFrequencySpinner.setValue(getAntiBanInt("mouseMovementFrequency", 30));
                mouseMovementFrequencySpinner.setEnabled(enabled);
            }

            // Camera movement
            if (enableCameraMovement != null) {
                boolean enabled = getAntiBanBoolean("enableCameraMovement", true);
                enableCameraMovement.setSelected(enabled);
                cameraMovementFrequencySpinner.setValue(getAntiBanInt("cameraMovementFrequency", 45));
                cameraMovementFrequencySpinner.setEnabled(enabled);
            }

            // Random breaks
            if (enableRandomBreaks != null) {
                boolean enabled = getAntiBanBoolean("enableRandomBreaks", true);
                enableRandomBreaks.setSelected(enabled);
                randomBreaksFrequencySpinner.setValue(getAntiBanInt("randomBreaksFrequency", 10));
                randomBreaksFrequencySpinner.setEnabled(enabled);
            }

            // Random AFK
            if (enableRandomAFK != null) {
                boolean enabled = getAntiBanBoolean("enableRandomAFK", true);
                enableRandomAFK.setSelected(enabled);
                randomAFKFrequencySpinner.setValue(getAntiBanInt("randomAFKFrequency", 15));
                randomAFKDurationSpinner.setValue(getAntiBanInt("randomAFKDuration", 10));
                randomAFKFrequencySpinner.setEnabled(enabled);
                randomAFKDurationSpinner.setEnabled(enabled);
            }

            // Examine objects
            if (enableExamineObjects != null) {
                boolean enabled = getAntiBanBoolean("enableExamineObjects", true);
                enableExamineObjects.setSelected(enabled);
                examineObjectsFrequencySpinner.setValue(getAntiBanInt("examineObjectsFrequency", 8));
                examineObjectsFrequencySpinner.setEnabled(enabled);
            }

            // Random tab checks
            if (enableRandomTabChecks != null) {
                boolean enabled = getAntiBanBoolean("enableRandomTabChecks", true);
                enableRandomTabChecks.setSelected(enabled);
                randomTabChecksFrequencySpinner.setValue(getAntiBanInt("randomTabChecksFrequency", 5));
                randomTabChecksFrequencySpinner.setEnabled(enabled);
            }

            // Hover items
            if (enableHoverItems != null) {
                boolean enabled = getAntiBanBoolean("enableHoverItems", true);
                enableHoverItems.setSelected(enabled);
                hoverItemsFrequencySpinner.setValue(getAntiBanInt("hoverItemsFrequency", 3));
                hoverItemsFrequencySpinner.setEnabled(enabled);
            }

            // Random right clicks
            if (enableRandomRightClicks != null) {
                boolean enabled = getAntiBanBoolean("enableRandomRightClicks", true);
                enableRandomRightClicks.setSelected(enabled);
                randomRightClicksFrequencySpinner.setValue(getAntiBanInt("randomRightClicksFrequency", 4));
                randomRightClicksFrequencySpinner.setEnabled(enabled);
            }
        }

        // Update config selector
        if (configSelector != null && currentConfigName != null) {
            configSelector.setSelectedItem(currentConfigName);
        }

        // Log the current state
        Logger.log("UI updated with " + Main.targetSkills.size() + " selected skills");

        // Refresh UI
        revalidate();
        repaint();
    }

    private void updateMainFromUI() {
        // Store the previous state of targetSkills
        Map<Skill, Integer> previousTargetSkills = new HashMap<>(Main.targetSkills);

        // Update Main.targetSkills from UI (F2P skills only)
        // Clear and rebuild targetSkills to ensure unchecked skills are removed
        Main.targetSkills.clear();

        // Only consider F2P skills
        for (Skill skill : F2P_SKILLS) {
            JCheckBox checkbox = skillCheckboxes.get(skill);
            if (checkbox != null && checkbox.isSelected()) {
                JSpinner spinner = skillLevelSpinners.get(skill);
                if (spinner != null) {
                    Main.targetSkills.put(skill, (int) spinner.getValue());
                    Logger.log("Enabled skill: " + skill.name() + " -> Level " + spinner.getValue());
                }
            } else {
                Logger.log("Disabled skill: " + skill.name());
            }
        }

        // Check if the enabled skills have changed
        boolean skillsChanged = false;

        // Check for skills that were added or removed
        if (previousTargetSkills.size() != Main.targetSkills.size()) {
            skillsChanged = true;
        } else {
            // Check if any skills were changed
            for (Map.Entry<Skill, Integer> entry : Main.targetSkills.entrySet()) {
                if (!previousTargetSkills.containsKey(entry.getKey())) {
                    skillsChanged = true;
                }
            }

            if (!skillsChanged) {
                // Check if any skills were removed
                for (Map.Entry<Skill, Integer> entry : previousTargetSkills.entrySet()) {
                    if (!Main.targetSkills.containsKey(entry.getKey())) {
                        skillsChanged = true;
                        break;
                    }
                }
            }
        }

        // If skills changed, reinitialize tasks and auto-select tasks for new skills
        if (skillsChanged) {
            Logger.log("Enabled skills changed. Reinitializing tasks...");
            TaskManager.initializeTasks();

            // Auto-select tasks for newly enabled skills
            autoSelectTasksForNewSkills(previousTargetSkills);
        }

        // Update general settings
        Main.antibanEnabled = enableAntiban.isSelected();
        Main.afkEnabled = enableProgressTracking.isSelected();
        Main.breaksEnabled = enableBreaks.isSelected();
        Main.breakFrequency = (int) breakFrequencySpinner.getValue();
        Main.breakDuration = (int) breakDurationSpinner.getValue();
        Main.logoutAfterTime = logoutAfterTime.isSelected();
        Main.logoutTime = (int) logoutTimeSpinner.getValue();

        // Update anti-ban settings
        if (Main.antiBanSettings == null) {
            Main.antiBanSettings = new HashMap<>();
        }

        // Mouse movement
        Main.antiBanSettings.put("enableMouseMovement", enableMouseMovement.isSelected());
        Main.antiBanSettings.put("mouseMovementFrequency", mouseMovementFrequencySpinner.getValue());

        // Camera movement
        Main.antiBanSettings.put("enableCameraMovement", enableCameraMovement.isSelected());
        Main.antiBanSettings.put("cameraMovementFrequency", cameraMovementFrequencySpinner.getValue());

        // Random breaks
        Main.antiBanSettings.put("enableRandomBreaks", enableRandomBreaks.isSelected());
        Main.antiBanSettings.put("randomBreaksFrequency", randomBreaksFrequencySpinner.getValue());

        // Random AFK
        Main.antiBanSettings.put("enableRandomAFK", enableRandomAFK.isSelected());
        Main.antiBanSettings.put("randomAFKFrequency", randomAFKFrequencySpinner.getValue());
        Main.antiBanSettings.put("randomAFKDuration", randomAFKDurationSpinner.getValue());

        // Examine objects
        Main.antiBanSettings.put("enableExamineObjects", enableExamineObjects.isSelected());
        Main.antiBanSettings.put("examineObjectsFrequency", examineObjectsFrequencySpinner.getValue());

        // Random tab checks
        Main.antiBanSettings.put("enableRandomTabChecks", enableRandomTabChecks.isSelected());
        Main.antiBanSettings.put("randomTabChecksFrequency", randomTabChecksFrequencySpinner.getValue());

        // Hover items
        Main.antiBanSettings.put("enableHoverItems", enableHoverItems.isSelected());
        Main.antiBanSettings.put("hoverItemsFrequency", hoverItemsFrequencySpinner.getValue());

        // Random right clicks
        Main.antiBanSettings.put("enableRandomRightClicks", enableRandomRightClicks.isSelected());
        Main.antiBanSettings.put("randomRightClicksFrequency", randomRightClicksFrequencySpinner.getValue());

        // Update selected tasks from UI
        updateSelectedTasksFromUI();

        // Force a complete rebuild of selected tasks to ensure no orphaned tasks
        forceRebuildSelectedTasks();

        // Debug logging
        debugCurrentState();

        Logger.log("Updated Main settings from UI");
    }

    private void saveSettings() {
        // First update Main settings from UI
        updateMainFromUI();

        // Then save to file
        try {
            File configFile = getConfigFile();
            Map<String, Object> config = new HashMap<>();

            // Save skills
            Map<String, Integer> targetSkills = new HashMap<>();
            List<String> enabledSkills = new ArrayList<>();

            Main.targetSkills.forEach((skill, level) -> {
                String skillName = skill.name();
                enabledSkills.add(skillName);
                targetSkills.put(skillName, level);
            });

            config.put("targetSkills", targetSkills);
            config.put("enabledSkills", enabledSkills);

            // Save tasks
            List<String> taskNames = new ArrayList<>();
            if (Main.selectedTasks != null) {
                Main.selectedTasks.forEach(task -> {
                    if (task instanceof AbstractTask) {
                        taskNames.add(((AbstractTask) task).getName());
                    } else if (task instanceof String) {
                        taskNames.add((String) task);
                    }
                });
            }
            config.put("selectedTasks", taskNames);

            // Save other settings
            config.put("antibanEnabled", Main.antibanEnabled);
            config.put("progressTrackingEnabled", Main.afkEnabled);
            config.put("breaksEnabled", Main.breaksEnabled);
            config.put("breakFrequency", Main.breakFrequency);
            config.put("breakDuration", Main.breakDuration);
            config.put("logoutAfterTime", Main.logoutAfterTime);
            config.put("logoutTime", Main.logoutTime);
            config.put("taskOptimization", Main.useTaskOptimization);

            // Save anti-ban settings
            config.put("antiBanSettings", Main.antiBanSettings);

            // Save banking preferences
            config.put("tasksThatBank", new ArrayList<>(Main.tasksThatBank));

            // Write to file
            try (FileWriter writer = new FileWriter(configFile)) {
                new GsonBuilder().setPrettyPrinting().create().toJson(config, writer);
                Logger.log("Settings saved to " + configFile.getAbsolutePath());
            }

            // Mark as not changed
            settingsChanged = false;

        } catch (Exception e) {
            Logger.log("Error saving settings: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Updates the UI state based on whether the bot is currently running
     */
    private void updateUIForBotStatus() {
        // Update the start button text based on bot status
        SwingUtilities.invokeLater(() -> {
            // Find and update the start button
            updateStartButtonText();

            if (Main.running) {
                // If bot is running, load current settings into UI
                updateUIFromCurrentSettings();
                Logger.log("GUI opened while bot is running - loaded current settings");
            }
        });
    }

    /**
     * Updates the start button text based on bot running status
     */
    private void updateStartButtonText() {
        // This will be called when the button panel is created
        // The button text is already set correctly in createButtonPanel()
    }

    /**
     * Updates the UI to reflect the current bot settings
     */
    private void updateUIFromCurrentSettings() {
        try {
            // Update skill selections and target levels
            for (Map.Entry<Skill, Integer> entry : Main.targetSkills.entrySet()) {
                Skill skill = entry.getKey();
                Integer targetLevel = entry.getValue();

                JCheckBox checkbox = skillCheckboxes.get(skill);
                JSpinner spinner = skillLevelSpinners.get(skill);

                if (checkbox != null) {
                    checkbox.setSelected(true);
                }
                if (spinner != null && targetLevel != null) {
                    spinner.setValue(targetLevel);
                }
            }

            // Update general settings
            if (enableAntiban != null) enableAntiban.setSelected(Main.antibanEnabled);
            if (enableProgressTracking != null) enableProgressTracking.setSelected(Main.afkEnabled);
            if (enableBreaks != null) enableBreaks.setSelected(Main.breaksEnabled);
            if (logoutAfterTime != null) logoutAfterTime.setSelected(Main.logoutAfterTime);

            if (breakFrequencySpinner != null) breakFrequencySpinner.setValue(Main.breakFrequency);
            if (breakDurationSpinner != null) breakDurationSpinner.setValue(Main.breakDuration);
            if (logoutTimeSpinner != null) logoutTimeSpinner.setValue(Main.logoutTime);

            // Update task selections
            updateTaskSelectionFromMain();

            Logger.log("UI updated with current bot settings");
        } catch (Exception e) {
            Logger.log("Error updating UI from current settings: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Auto-selects tasks for newly enabled skills
     * @param previousTargetSkills The skills that were enabled before the update
     */
    private void autoSelectTasksForNewSkills(Map<Skill, Integer> previousTargetSkills) {
        try {
            if (Main.selectedTasks == null) {
                Main.selectedTasks = new ArrayList<>();
            }

            // Find newly enabled skills
            for (Map.Entry<Skill, Integer> entry : Main.targetSkills.entrySet()) {
                Skill skill = entry.getKey();

                // If this skill wasn't enabled before, it's a new skill
                if (!previousTargetSkills.containsKey(skill)) {
                    Logger.log("Auto-selecting tasks for newly enabled skill: " + skill.name());

                    // Get all available tasks for this skill
                    List<AbstractTask> availableTasks = TaskManager.getTasksForSkill(skill);

                    if (!availableTasks.isEmpty()) {
                        // Add all tasks for this skill (user can deselect later if needed)
                        for (AbstractTask task : availableTasks) {
                            // CRITICAL: Double-check that the task's skill matches the enabled skill
                            if (!task.getSkill().equals(skill)) {
                                Logger.log("WARNING: Task " + task.getName() + " claims to be for skill " +
                                          skill.name() + " but actually returns skill " + task.getSkill().name() + ". Skipping.");
                                continue;
                            }

                            // Check if task is not already selected
                            boolean alreadySelected = Main.selectedTasks.stream().anyMatch(selectedTask -> {
                                if (selectedTask instanceof AbstractTask) {
                                    return ((AbstractTask) selectedTask).getName().equals(task.getName());
                                } else if (selectedTask instanceof String) {
                                    return selectedTask.equals(task.getName());
                                }
                                return false;
                            });

                            if (!alreadySelected) {
                                Main.selectedTasks.add(task);
                                Logger.log("Auto-selected task: " + task.getName() + " (skill: " + task.getSkill().name() + ") for enabled skill: " + skill.name());
                            } else {
                                Logger.log("Task " + task.getName() + " already selected, skipping");
                            }
                        }
                    } else {
                        Logger.log("No tasks available for skill: " + skill.name());
                    }
                }
            }

            Logger.log("Auto-selection complete. Total selected tasks: " + Main.selectedTasks.size());

        } catch (Exception e) {
            Logger.log("Error auto-selecting tasks for new skills: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Updates Main.selectedTasks based on current UI selections
     * This method preserves the current task selections that were made in the UI
     */
    private void updateSelectedTasksFromUI() {
        try {
            // The task selections are maintained by the updateTaskSelection method
            // when users interact with task checkboxes in the UI.
            // However, we need to clean up the selectedTasks list to remove any tasks
            // whose skills are no longer enabled, and ensure all tasks for enabled
            // skills are properly included.

            if (Main.selectedTasks == null) {
                Main.selectedTasks = new ArrayList<>();
            }

            // Create a new list to avoid concurrent modification
            ArrayList<Object> validTasks = new ArrayList<>();

            // First, preserve existing valid task selections
            for (Object taskObj : Main.selectedTasks) {
                AbstractTask task = null;

                if (taskObj instanceof AbstractTask) {
                    task = (AbstractTask) taskObj;
                } else if (taskObj instanceof String) {
                    task = TaskManager.getTaskByName((String) taskObj);
                }

                // Only keep tasks whose skills are still enabled
                if (task != null && Main.targetSkills.containsKey(task.getSkill())) {
                    validTasks.add(taskObj);
                    Logger.log("Keeping task: " + task.getName() + " (skill " + task.getSkill().name() + " is enabled)");
                } else if (task != null) {
                    Logger.log("Removing task: " + task.getName() + " - skill " + task.getSkill().name() + " is no longer enabled");
                } else {
                    Logger.log("Removing invalid task object: " + taskObj);
                }
            }

            // Update the selected tasks list
            Main.selectedTasks.clear();
            Main.selectedTasks.addAll(validTasks);

            Logger.log("Updated selected tasks from UI. Total selected: " + Main.selectedTasks.size());

            // Additional validation: Log all remaining tasks to verify they're for enabled skills
            Logger.log("=== TASK VALIDATION AFTER CLEANUP ===");
            for (Object taskObj : Main.selectedTasks) {
                AbstractTask task = null;
                if (taskObj instanceof AbstractTask) {
                    task = (AbstractTask) taskObj;
                } else if (taskObj instanceof String) {
                    task = TaskManager.getTaskByName((String) taskObj);
                }

                if (task != null) {
                    boolean skillEnabled = Main.targetSkills.containsKey(task.getSkill());
                    Logger.log("Task: " + task.getName() + " | Skill: " + task.getSkill().name() + " | Enabled: " + skillEnabled);

                    if (!skillEnabled) {
                        Logger.log("ERROR: Found task for disabled skill! This should not happen.");
                    }
                }
            }
            Logger.log("=== END TASK VALIDATION ===");

            // If we have no selected tasks but have enabled skills, this might be because
            // the user added skills but didn't manually select tasks. In this case,
            // we should ensure at least some tasks are available.
            if (Main.selectedTasks.isEmpty() && !Main.targetSkills.isEmpty()) {
                Logger.log("No tasks selected but skills are enabled. This may cause the bot to stop.");
                Logger.log("Consider auto-selecting tasks or the bot will attempt auto-selection during execution.");
            }

        } catch (Exception e) {
            Logger.log("Error updating selected tasks from UI: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Force a complete rebuild of selected tasks to ensure no orphaned tasks
     */
    private void forceRebuildSelectedTasks() {
        try {
            Logger.log("=== FORCE REBUILDING SELECTED TASKS ===");

            // Store current selected task names for reference
            List<String> currentTaskNames = new ArrayList<>();
            if (Main.selectedTasks != null) {
                for (Object taskObj : Main.selectedTasks) {
                    if (taskObj instanceof AbstractTask) {
                        currentTaskNames.add(((AbstractTask) taskObj).getName());
                    } else if (taskObj instanceof String) {
                        currentTaskNames.add((String) taskObj);
                    }
                }
            }

            Logger.log("Previous selected tasks: " + currentTaskNames);

            // Clear the selected tasks completely
            if (Main.selectedTasks == null) {
                Main.selectedTasks = new ArrayList<>();
            } else {
                Main.selectedTasks.clear();
            }

            // Rebuild from scratch using only tasks for enabled skills
            for (Map.Entry<Skill, Integer> entry : Main.targetSkills.entrySet()) {
                Skill skill = entry.getKey();
                Logger.log("Rebuilding tasks for enabled skill: " + skill.name());

                // Get all available tasks for this skill
                List<AbstractTask> availableTasks = TaskManager.getTasksForSkill(skill);

                for (AbstractTask task : availableTasks) {
                    // Double-check the task's skill matches
                    if (task.getSkill().equals(skill)) {
                        // Only add if it was previously selected or if it's a new skill
                        if (currentTaskNames.contains(task.getName())) {
                            Main.selectedTasks.add(task);
                            Logger.log("Restored task: " + task.getName() + " for skill: " + skill.name());
                        }
                    } else {
                        Logger.log("CRITICAL ERROR: Task " + task.getName() + " returned by TaskManager for skill " +
                                  skill.name() + " but task claims skill " + task.getSkill().name());
                    }
                }
            }

            // If we have no tasks but have enabled skills, auto-select all tasks for enabled skills
            if (Main.selectedTasks.isEmpty() && !Main.targetSkills.isEmpty()) {
                Logger.log("No tasks selected after rebuild, auto-selecting all tasks for enabled skills");
                for (Map.Entry<Skill, Integer> entry : Main.targetSkills.entrySet()) {
                    Skill skill = entry.getKey();
                    List<AbstractTask> availableTasks = TaskManager.getTasksForSkill(skill);
                    for (AbstractTask task : availableTasks) {
                        if (task.getSkill().equals(skill)) {
                            Main.selectedTasks.add(task);
                            Logger.log("Auto-selected task: " + task.getName() + " for skill: " + skill.name());
                        }
                    }
                }
            }

            Logger.log("Force rebuild complete. Total tasks: " + Main.selectedTasks.size());
            Logger.log("=== END FORCE REBUILD ===");

        } catch (Exception e) {
            Logger.log("Error in forceRebuildSelectedTasks: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Debug method to log current state
     */
    private void debugCurrentState() {
        Logger.log("=== DEBUG: Current State After Settings Update ===");
        Logger.log("Target Skills: " + Main.targetSkills.size());
        for (Map.Entry<Skill, Integer> entry : Main.targetSkills.entrySet()) {
            Logger.log("  - " + entry.getKey().name() + " -> Level " + entry.getValue());
        }

        Logger.log("Selected Tasks: " + (Main.selectedTasks != null ? Main.selectedTasks.size() : 0));
        if (Main.selectedTasks != null) {
            for (Object task : Main.selectedTasks) {
                if (task instanceof AbstractTask) {
                    AbstractTask abstractTask = (AbstractTask) task;
                    Logger.log("  - " + abstractTask.getName() + " (" + abstractTask.getSkill().name() + ")");
                } else {
                    Logger.log("  - " + task.toString());
                }
            }
        }

        Logger.log("Current Active Task: " + (Main.currentActiveTask != null ? Main.currentActiveTask.getName() : "None"));
        Logger.log("Bot Running: " + Main.running);
        Logger.log("=== END DEBUG ===");
    }

    /**
     * Updates task selection UI based on Main.selectedTasks
     */
    private void updateTaskSelectionFromMain() {
        if (Main.selectedTasks == null) return;

        // Clear current selections first
        // Then update based on Main.selectedTasks
        // This ensures the UI reflects the actual running configuration
    }

    private void resetSettings() {
        // Reset all skills
        resetAllSkillsUI();

        // Reset other settings
        enableAntiban.setSelected(true);
        enableProgressTracking.setSelected(true);
        enableBreaks.setSelected(false);
        logoutAfterTime.setSelected(false);

        breakFrequencySpinner.setValue(60);
        breakDurationSpinner.setValue(5);
        logoutTimeSpinner.setValue(120);

        breakFrequencySpinner.setEnabled(false);
        breakDurationSpinner.setEnabled(false);
        logoutTimeSpinner.setEnabled(false);

        // Clear selected tasks
        if (Main.selectedTasks != null) {
            Main.selectedTasks.clear();
        }

        // Hide task panel if visible
        if (taskPanel != null) {
            taskPanel.setVisible(false);
        }

        Logger.log("Settings reset to defaults");
    }

    private boolean getConfigBoolean(Map<String, Object> config, String key, boolean defaultValue) {
        Object value = config.get(key);
        return value instanceof Boolean ? (Boolean) value : defaultValue;
    }

    private Number getConfigNumber(Map<String, Object> config, String key, Number defaultValue) {
        Object value = config.get(key);
        return value instanceof Number ? (Number) value : defaultValue;
    }

    private boolean getAntiBanBoolean(String key, boolean defaultValue) {
        if (Main.antiBanSettings == null) return defaultValue;
        Object value = Main.antiBanSettings.get(key);
        return value instanceof Boolean ? (Boolean) value : defaultValue;
    }

    private int getAntiBanInt(String key, int defaultValue) {
        if (Main.antiBanSettings == null) return defaultValue;
        Object value = Main.antiBanSettings.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private void resetAllSkillsUI() {
        Logger.log("Resetting all skills UI");
        // Don't clear Main.targetSkills here - we need to preserve the loaded skills
        // Main.targetSkills.clear();

        // Only reset F2P skills
        for (Skill skill : F2P_SKILLS) {
            JCheckBox checkbox = skillCheckboxes.get(skill);
            JSpinner spinner = skillLevelSpinners.get(skill);
            JPanel panel = skillPanels.get(skill);

            if (checkbox != null && spinner != null && panel != null) {
                // Reset checkbox and spinner
                checkbox.setSelected(true);
                spinner.setValue(99);
                spinner.setEnabled(true);

                // Reset visual styling
                panel.setBorder(BorderFactory.createLineBorder(BORDER_COLOR, 1, true));

                // Preserve highlight if this is the highlighted skill
                if (skill == highlightedSkill) {
                    panel.setBackground(HIGHLIGHT_COLOR);
                } else {
                    panel.setBackground(PANEL_COLOR);
                }

                // Reset text field background
                JComponent editor = spinner.getEditor();
                JFormattedTextField textField = ((JSpinner.DefaultEditor) editor).getTextField();
                textField.setBackground(PANEL_COLOR);

                // Reset label styling
                for (Component comp : panel.getComponents()) {
                    if (comp instanceof JLabel && !(((JLabel)comp).getIcon() instanceof ImageIcon)) {
                        comp.setFont(new Font("Segoe UI", Font.PLAIN, 12));
                        comp.setForeground(Color.LIGHT_GRAY);
                        break;
                    }
                }
            }
        }

        Logger.log("All skills UI reset, Main.targetSkills size: " + Main.targetSkills.size());
    }

    private void updateSkillUI(Skill skill, int level, boolean selected) {
        Logger.log("updateSkillUI called for " + skill.name() + ", level=" + level + ", selected=" + selected);

        try {
            JCheckBox checkbox = skillCheckboxes.get(skill);
            JSpinner spinner = skillLevelSpinners.get(skill);
            JPanel panel = skillPanels.get(skill);

            if (checkbox == null) Logger.log("Checkbox is null for " + skill.name());
            if (spinner == null) Logger.log("Spinner is null for " + skill.name());
            if (panel == null) Logger.log("Panel is null for " + skill.name());

            if (checkbox != null && spinner != null && panel != null) {
                Logger.log("Setting UI components for " + skill.name());

                // Force the checkbox to be selected
                checkbox.setSelected(selected);

                // Set the spinner value and enable/disable it
                spinner.setValue(level);
                spinner.setEnabled(selected);

                // Update visual styling with a consistent border for selected skills
                Color borderColor = selected ? SELECTED_COLOR : BORDER_COLOR;
                int borderThickness = selected ? 2 : 1;
                panel.setBorder(BorderFactory.createLineBorder(borderColor, borderThickness, true));

                // Preserve highlight if this is the highlighted skill
                if (skill == highlightedSkill) {
                    panel.setBackground(HIGHLIGHT_COLOR);
                }

                // Update the text field background in the spinner with consistent colors
                JComponent editor = spinner.getEditor();
                JFormattedTextField textField = ((JSpinner.DefaultEditor) editor).getTextField();
                textField.setBackground(selected ? SELECTED_BG_COLOR : PANEL_COLOR);
                textField.setForeground(selected ? SELECTED_TEXT_COLOR : Color.LIGHT_GRAY);

                // Update label styling to be bold for selected skills
                for (Component comp : panel.getComponents()) {
                    if (comp instanceof JLabel) {
                        JLabel label = (JLabel) comp;
                        if (!(label.getIcon() instanceof ImageIcon)) {
                            label.setFont(new Font("Segoe UI", selected ? Font.BOLD : Font.PLAIN, 12));
                            label.setForeground(selected ? SELECTED_TEXT_COLOR : Color.LIGHT_GRAY);
                        }
                    }
                }

                // Update main target skills
                if (selected) {
                    Main.targetSkills.put(skill, level);
                } else {
                    Main.targetSkills.remove(skill);
                }

                // Force the panel to repaint
                panel.revalidate();
                panel.repaint();

                Logger.log("UI updated for " + skill.name() + ", selected=" + checkbox.isSelected());
            } else {
                Logger.log("Failed to update UI for " + skill.name() + " - some components are null");
            }
        } catch (Exception e) {
            Logger.log("Error updating UI for " + skill.name() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
}


