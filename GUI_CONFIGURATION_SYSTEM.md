# GUI Configuration System - Open GUI Button & Dynamic Settings

The bot now supports opening the configuration GUI while the script is running and dynamically updating settings without stopping the bot.

## New Features

### 1. **Open GUI Button**
- **Location**: Appears in the paint overlay next to the "Skip Task" button
- **Color**: Steel blue background for easy identification
- **Function**: Opens the configuration GUI at any time during bot execution

### 2. **Dynamic Start Button**
The start button in the GUI now changes behavior based on bot status:

- **When Bot is NOT Running**: Shows "Start Script" - starts the bot and closes GUI
- **When Bot IS Running**: Shows "Update Settings" - updates settings and closes GUI

### 3. **Live Settings Update**
When the GUI is opened while the bot is running:
- **Current settings are loaded** into the GUI automatically
- **All changes are applied immediately** when clicking "Update Settings"
- **No need to stop and restart** the bot to change configurations

## How to Use

### **Opening GUI While Bot is Running**
1. **Click the "Open GUI" button** in the paint overlay (steel blue button)
2. **GUI opens with current settings** already loaded
3. **Make your changes** to skills, target levels, anti-ban settings, etc.
4. **Click "Update Settings"** to apply changes immediately
5. **Bot continues running** with new settings

### **Starting Bot from GUI**
1. **Configure your settings** in the GUI
2. **Click "Start Script"** to begin bot execution
3. **GUI closes** and bot starts with your settings

### **Updating Settings During Execution**
- **Skills**: Add/remove skills, change target levels
- **Anti-Ban**: Enable/disable features, adjust frequencies
- **Breaks**: Modify break settings, logout timers
- **Tasks**: Select/deselect specific tasks for skills
- **Configurations**: Load different saved configurations

## Visual Indicators

### **Paint Overlay Buttons**
```
[Open GUI] [Skip Task]
```
- **Open GUI**: Steel blue button - opens configuration interface
- **Skip Task**: Dark gray button - skips current task

### **GUI Button States**
- **"Start Script"**: Green button when bot is not running
- **"Update Settings"**: Green button when bot is running

## Technical Implementation

### **Button Click Detection**
- Both buttons use rectangle-based click detection
- Proper spacing and positioning in paint overlay
- Thread-safe GUI creation using SwingUtilities

### **Settings Synchronization**
- **GUI → Main**: `updateMainFromUI()` method transfers all settings
- **Main → GUI**: `updateUIFromCurrentSettings()` loads current bot state
- **Real-time updates**: Settings take effect immediately without restart

### **State Management**
- **Bot status detection**: GUI checks `Main.running` flag
- **Dynamic button text**: Changes based on current bot state
- **Setting preservation**: Current configuration is maintained when GUI opens

## Benefits

1. **No Downtime**: Change settings without stopping the bot
2. **Real-time Adjustments**: Respond to changing game conditions
3. **Easy Access**: Quick GUI access via paint overlay button
4. **Configuration Management**: Load/save different setups on the fly
5. **User Friendly**: Clear visual indicators and intuitive workflow

## Example Workflows

### **Changing Target Levels Mid-Run**
1. Click "Open GUI" button
2. Adjust target levels for active skills
3. Click "Update Settings"
4. Bot immediately uses new target levels

### **Switching Task Focus**
1. Open GUI while bot is running
2. Disable current skill, enable different skill
3. Select tasks for new skill
4. Update settings - bot switches to new tasks

### **Adjusting Anti-Ban Settings**
1. Open GUI during bot execution
2. Modify anti-ban frequencies or features
3. Apply changes - new anti-ban behavior takes effect immediately

### **Loading Different Configuration**
1. Open GUI while bot is running
2. Select different configuration from dropdown
3. Click "Load" to load new settings
4. Click "Update Settings" to apply new configuration

This system provides maximum flexibility for bot configuration and management, allowing users to adapt their bot's behavior in real-time without interrupting execution!
