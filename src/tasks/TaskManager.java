package tasks;

import Main.Main;
import java.util.*;
import java.util.stream.Collectors;

import org.dreambot.api.Client;
import org.dreambot.api.data.GameState;
import org.dreambot.api.methods.skills.Skill;
import org.dreambot.api.methods.tabs.Tabs;
import org.dreambot.api.script.ScriptManager;
import org.dreambot.api.utilities.Logger;
import org.dreambot.api.utilities.Sleep;

import static org.dreambot.api.data.GameState.LOGIN_SCREEN;

/** Manages all tasks in the bot */
public class TaskManager {
  private static final Map<Skill, List<AbstractTask>> tasksBySkill = new HashMap<>();
  private static final List<AbstractTask> allTasks = new ArrayList<>();

  /** Initialize all tasks */
  public static void initializeTasks() {
    // Clear existing tasks
    tasksBySkill.clear();
    allTasks.clear();

    Logger.log("TaskManager: Initializing tasks...");

    // Only register tasks for enabled skills
    if (Main.targetSkills.containsKey(Skill.ATTACK)) {
      registerAttackTasks();
    } else {
      Logger.log("TaskManager: Skipping Attack tasks (skill not enabled)");
    }

    if (Main.targetSkills.containsKey(Skill.STRENGTH)) {
      registerStrengthTasks();
    } else {
      Logger.log("TaskManager: Skipping Strength tasks (skill not enabled)");
    }

    if (Main.targetSkills.containsKey(Skill.MAGIC)) {
      registerMagicTasks();
    } else {
      Logger.log("TaskManager: Skipping Magic tasks (skill not enabled)");
    }

    if (Main.targetSkills.containsKey(Skill.RANGED)) {
      registerRangedTasks();
    } else {
      Logger.log("TaskManager: Skipping Ranged tasks (skill not enabled)");
    }

    if (Main.targetSkills.containsKey(Skill.MINING)) {
      registerMiningTasks();
    } else {
      Logger.log("TaskManager: Skipping Mining tasks (skill not enabled)");
    }
    if (Main.targetSkills.containsKey(Skill.FISHING)) {
      registerFishingTasks();
    } else {
      Logger.log("TaskManager: Skipping Fishing tasks (skill not enabled)");
    }
    if (Main.targetSkills.containsKey(Skill.COOKING)) {
      registerCookingTask();
    } else {
      Logger.log("TaskManager: Skipping Cooking tasks (skill not enabled)");
    }
    if (Main.targetSkills.containsKey(Skill.WOODCUTTING)) {
      registerWoodcuttingTasks();
    } else {
      Logger.log("TaskManager: Skipping Woodcutting tasks (skill not enabled)");
    }

    Logger.log(
        "TaskManager: Initialized "
            + allTasks.size()
            + " tasks across "
            + tasksBySkill.size()
            + " skills");

    // Debug output of all registered tasks
    for (Map.Entry<Skill, List<AbstractTask>> entry : tasksBySkill.entrySet()) {
      Logger.log(
          "TaskManager: Skill "
              + entry.getKey().name()
              + " has "
              + entry.getValue().size()
              + " tasks");
      for (AbstractTask task : entry.getValue()) {
        Logger.log("TaskManager: - Task: " + task.getName());
      }
    }
  }

  /** Register mining tasks */
  private static void registerMiningTasks() {
    try {
      registerTask(new tasks.Mining.Lumbridge.LumbridgeCopperMineSouth());
      registerTask(new tasks.Mining.Lumbridge.LumbridgeTinMineSouth());

      Logger.log("TaskManager: Successfully registered mining tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering mining tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  private static void registerAttackTasks() {
    try {
      // Register combat tasks LOWEST REQUIREMENT TO HIGHEST
      registerTask(new tasks.combat.MeleeCombat.Attack.Lumbridge.Chicken());
      registerTask(new tasks.combat.MeleeCombat.Attack.Lumbridge.Cows());

      Logger.log("TaskManager: Successfully registered Attack tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering Attack tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  private static void registerStrengthTasks() {
    try {
      // Register combat tasks
      registerTask(new tasks.combat.MeleeCombat.Strength.Lumbridge.Cows());

      Logger.log("TaskManager: Successfully registered Strength tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering Strength tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  public static void registerMagicTasks() {
    try {
      // Register combat tasks

      Logger.log("TaskManager: Successfully registered Magic tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering Magic tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  public static void registerRangedTasks() {
    try {
      // Register combat tasks

      Logger.log("TaskManager: Successfully registered Ranged tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering Ranged tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  public static void registerFishingTasks() {
    try {
      // Register combat tasks
      registerTask(new tasks.fishing.Lumbridge.Shrimps());

      Logger.log("TaskManager: Successfully registered Fishing tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering Fishing tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }
  public static void registerCookingTask(){
    try {
      // Register combat tasks
      registerTask(new tasks.cooking.Alkharid.Shrimps());

      Logger.log("TaskManager: Successfully registered Cooking tasks");
    }
    catch (Exception e) {
      Logger.log("TaskManager: Error registering Cooking tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Register woodcutting tasks
   */
  public static void registerWoodcuttingTasks() {
    try {
      // Register test task for location images
      registerTask(new tasks.test.TestLocationImageTask());

      Logger.log("TaskManager: Successfully registered Woodcutting tasks");
    } catch (Exception e) {
      Logger.log("TaskManager: Error registering Woodcutting tasks: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Register a task
   *
   * @param task The task to register
   */
  public static void registerTask(AbstractTask task) {
    if (task == null) {
      Logger.log("TaskManager: Attempted to register null task!");
      return;
    }

    // Add to all tasks list
    allTasks.add(task);

    // Add to skill-specific list
    Skill skill = task.getSkill();
    if (skill == null) {
      Logger.log("TaskManager: Task " + task.getName() + " has null skill!");
      return;
    }

    if (!tasksBySkill.containsKey(skill)) {
      tasksBySkill.put(skill, new ArrayList<>());
      Logger.log("TaskManager: Created new list for skill: " + skill.name());
    }

    tasksBySkill.get(skill).add(task);

    Logger.log("TaskManager: Registered task: " + task.getName() + " for skill: " + skill.name());
  }

  /**
   * Get all tasks for a specific skill
   *
   * @param skill The skill to get tasks for
   * @return List of tasks for the skill
   */
  public static List<AbstractTask> getTasksForSkill(Skill skill) {
    return tasksBySkill.getOrDefault(skill, Collections.emptyList());
  }

  /**
   * Get a task by name
   *
   * @param name The name of the task
   * @return The task, or null if not found
   */
  public static AbstractTask getTaskByName(String name) {
    return allTasks.stream().filter(task -> task.getName().equals(name)).findFirst().orElse(null);
  }

  /**
   * Get all registered tasks
   *
   * @return List of all registered tasks
   */
  public static List<AbstractTask> getAllTasks() {
    return new ArrayList<>(allTasks);
  }

  /**
   * Select the best task from the selected tasks
   *
   * @param selectedTasks List of tasks to choose from
   * @param targetSkills Map of skills and their target levels
   * @return The best task to execute
   */
  public static AbstractTask selectBestFromSelectedTasks(
      List<AbstractTask> selectedTasks, Map<Skill, Integer> targetSkills) {
    // Check if current task has reached its duration
    if (Main.currentActiveTask != null && Main.currentActiveTask.hasReachedDuration()) {
      Logger.log(
          "Current task '"
              + Main.currentActiveTask.getName()
              + "' has reached its duration. Selecting a new task.");
      // Reset the timer for the current task
      Main.currentActiveTask.resetTimer();
      Main.currentActiveTask = null;
    }

    // If we already have a task and it hasn't reached its duration, continue with it
    if (Main.currentActiveTask != null) {
      // Make sure the task is still eligible and its skill is enabled
      Skill taskSkill = Main.currentActiveTask.getSkill();
      boolean skillEnabled = targetSkills.containsKey(taskSkill);

      if (Main.currentActiveTask.accept()
          && selectedTasks.contains(Main.currentActiveTask)
          && skillEnabled) {
        return Main.currentActiveTask;
      } else {
        if (!skillEnabled) {
          Logger.log(
              "Current task '"
                  + Main.currentActiveTask.getName()
                  + "' has disabled skill '"
                  + taskSkill.name()
                  + "'. Selecting a new task.");
        } else {
          Logger.log(
              "Current task '"
                  + Main.currentActiveTask.getName()
                  + "' is no longer eligible. Selecting a new task.");
        }
        Main.currentActiveTask = null;
      }
    }

    // Filter tasks that meet requirements, haven't failed recently, and belong to enabled skills
    // Use traditional loop instead of streams for better performance
    List<AbstractTask> eligibleTasks = new ArrayList<>();
    for (AbstractTask task : selectedTasks) {
      // Skip tasks that have been marked as failed recently
      if (task.isFailedRecently()) {
        if (Main.DEBUG) {
          Logger.log("Skipping task '" + task.getName() + "' because it was marked as failed recently");
        }
        continue;
      }

      // Skip tasks whose skill is not enabled in targetSkills
      Skill taskSkill = task.getSkill();
      if (!targetSkills.containsKey(taskSkill)) {
        if (Main.DEBUG) {
          Logger.log("Skipping task '" + task.getName() + "' because its skill '" + taskSkill.name() + "' is not enabled");
        }
        continue;
      }

      // Check if the task accepts execution
      if (task.accept()) {
        eligibleTasks.add(task);
      }
    }

    if (eligibleTasks.isEmpty()) {
      // No eligible tasks found - check if we can allow task repetition
      Logger.log("No eligible tasks found. Checking if task repetition is allowed...");

      // Check if there are tasks that were marked as failed recently but could be repeated
      List<AbstractTask> repeatableTasks = new ArrayList<>();
      for (AbstractTask task : selectedTasks) {
        // Skip tasks whose skill is not enabled
        Skill taskSkill = task.getSkill();
        if (!targetSkills.containsKey(taskSkill)) {
          continue;
        }

        // Check if task has requirements and accepts execution (ignoring failed recently status)
        if (task.hasRequiredItems() && task.accept()) {
          repeatableTasks.add(task);
          Logger.log("Task '" + task.getName() + "' is repeatable (has requirements and accepts execution)");
        }
      }

      // If we have repeatable tasks, allow them to run again
      if (!repeatableTasks.isEmpty()) {
        Logger.log("Found " + repeatableTasks.size() + " repeatable task(s). Allowing task repetition.");

        // Clear the failed status for repeatable tasks to allow them to run again
        for (AbstractTask task : repeatableTasks) {
          task.clearFailedStatus();
          Logger.log("Cleared failed status for task: " + task.getName());
        }

        // Add repeatable tasks to eligible tasks
        eligibleTasks.addAll(repeatableTasks);
      } else {
        // No repeatable tasks found - stop the bot
        Logger.log("No repeatable tasks found. All tasks have been completed or cannot be executed.");
        Logger.log("Stopping the bot...");

        // Set a message to show the user
        Main.currentTask = "All tasks completed!";
        Main.taskTimeRemaining = "Bot stopping";

        Tabs.logout();
        Sleep.sleepUntil(() -> Client.getGameState() == GameState.LOGIN_SCREEN,500);

        ScriptManager.getScriptManager().stop();

        return null;
      }
    }

    // Select a random task that's different from the previous one
    AbstractTask newTask;
    AbstractTask previousTask = Main.currentActiveTask; // Store the previous task
    Random taskRandom = new Random(); // Cache random instance

    if (eligibleTasks.size() > 1 && previousTask != null) {
      // If we have more than one eligible task and had a previous task,
      // try to find a different task without creating a new list
      List<AbstractTask> tasksWithoutPrevious = new ArrayList<>();
      for (AbstractTask task : eligibleTasks) {
        if (!task.getName().equals(previousTask.getName())) {
          tasksWithoutPrevious.add(task);
        }
      }

      if (!tasksWithoutPrevious.isEmpty()) {
        // Select a random task from the filtered list
        newTask = tasksWithoutPrevious.get(taskRandom.nextInt(tasksWithoutPrevious.size()));
        if (Main.DEBUG) {
          Logger.log("Selected a different task from the previous one");
        }
      } else {
        // If somehow all tasks were filtered out, fall back to the original list
        newTask = eligibleTasks.get(taskRandom.nextInt(eligibleTasks.size()));
        if (Main.DEBUG) {
          Logger.log("Could not find a different task, using random task");
        }
      }
    } else {
      // If we only have one eligible task or had no previous task,
      // just select a random task from all eligible tasks
      newTask = eligibleTasks.get(taskRandom.nextInt(eligibleTasks.size()));
    }

    // Start the timer for the new task
    newTask.startTimer();

    // Set as current active task
    Main.currentActiveTask = newTask;

    if (previousTask != null) {
      Logger.log(
          "Switched from '"
              + previousTask.getName()
              + "' to '"
              + newTask.getName()
              + "' with duration: "
              + newTask.getDurationMinutes()
              + " minutes");
    } else {
      Logger.log(
          "Selected new task: '"
              + newTask.getName()
              + "' with duration: "
              + newTask.getDurationMinutes()
              + " minutes");
    }

    return newTask;
  }
}
