package tasks.Mining.Varrock;

import Main.Main;
import Utils.KazeUtils;
import org.dreambot.api.methods.container.impl.Inventory;
import org.dreambot.api.methods.interactive.Players;
import org.dreambot.api.methods.map.Area;
import org.dreambot.api.methods.skills.Skill;
import org.dreambot.api.methods.walking.impl.Walking;
import org.dreambot.api.methods.map.Tile;
import org.dreambot.api.methods.container.impl.bank.Bank;
import org.dreambot.api.methods.container.impl.bank.BankLocation;
import tasks.AbstractTask;

/**
 * Task for mining at the southwest Varrock mine
 */
public class VarrockMineSW extends AbstractTask {
    // Define a proper area for the SW Varrock mine (southwest of Varrock)
    private static final Area MINE_AREA = new Area(3172, 3368, 3184, 3380);

    // Define the nearest bank (Varrock West Bank)
    private static final BankLocation BANK_LOCATION = BankLocation.VARROCK_WEST;

    // Correct rock names as they appear in-game
    private static final String[] ROCK_TYPES = {"Iron rocks"};

    public VarrockMineSW() {
        // This task supports banking
        setSupportsBanking(true);
    }
    @Override
    public String getName() {
        return "Varrock SW Mining";
    }

    @Override
    public Skill getSkill() {
        return Skill.MINING;
    }

    @Override
    public String getRequirements() {
        return "Level 1 Mining";
    }

    @Override
    public String getLocation() {
        return "Varrock";
    }

    @Override
    public String getDescription() {
        return "Mines iron ore at the southwest Varrock mine.";
    }

    /**
     * Override getTaskArea to provide a custom mining area
     * This demonstrates using an Area instead of just a Tile
     */
    @Override
    public Area getTaskArea() {
        return MINE_AREA;
    }

    /**
     * Override getTaskLocation to provide a central tile within the mining area
     * This demonstrates providing both Area and Tile support
     */
    @Override
    public Tile getTaskLocation() {
        // Return the center of the mining area
        return new Tile(3178, 3374, 0);
    }

    @Override
    public boolean accept() {
        return true; // No specific requirements for this task
    }

    @Override
    public int execute() {
        // Determine the current state
        String state;

        // Check if we should bank or drop ores
        boolean shouldBank = Main.tasksThatBank.contains(getName());

        if (Inventory.isFull()) {
            if (shouldBank) {
                // If we're at the bank, deposit ores
                if (Bank.isOpen() || BANK_LOCATION.getArea(5).contains(Players.getLocal())) {
                    state = "DEPOSIT_ORES";
                } else {
                    // Otherwise, walk to the bank
                    state = "WALK_TO_BANK";
                }
            } else {
                // If not banking, drop ores
                state = "DROP_ORES";
            }
        } else if (!isAtTaskLocation() && !Bank.isOpen() && Inventory.isEmpty()) {
            state = "WALK_TO_MINE";
        } else if (!Players.getLocal().isAnimating() && !Bank.isOpen()) {
            state = "START_MINING";
        } else {
            state = "CONTINUE_MINING";
        }

        // Handle each state with a switch
        switch (state) {
            case "DROP_ORES":
                dropOres();
                return 600;

            case "WALK_TO_BANK":
                Main.action = getName() + " - Walking to bank";
                KazeUtils.walkToBankLocation(BANK_LOCATION);
                return 1000;

            case "DEPOSIT_ORES":
                if (!Bank.isOpen()) {
                    Bank.open(BANK_LOCATION);
                    return 1000;
                }
                Bank.depositAllItems();
                Bank.close();
                return 600;

            case "WALK_TO_MINE":
                Main.action = getName() + " - Walking to mine";
                KazeUtils.walkToTaskLocation(this);
                return 1000;

            case "START_MINING":

                if (KazeUtils.mineRock(1, ROCK_TYPES)) {
                    return 1000;
                } else {
                    // No rocks available, wait a bit
                    return 2000;
                }

            case "CONTINUE_MINING":
            default:
                // Already mining, wait
                return 1000;
        }
    }

    private void dropOres() {
        Inventory.dropAll("Tin ore", "Copper ore", "Iron ore");
    }
}